package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.HashMap;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.LinkedHashMap;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Map;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Set;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 检测DanaBot恶意软件使用的证书
 */
@Slf4j
public class DanaBotDetector extends BaseCertificateDetector {
    
    public DanaBotDetector() {
        super("DanaBot Detector");
    }
    
    protected boolean hasAllRequiredFields(Map<String, String> map, String... keys) {
        if (map == null) return false;
        for (String key : keys) {
            if (!map.containsKey(key)) {
                return false;
            }
        }
        return true;
    }
    
    protected String getStringValue(Map<String, String> map, String key) {
        if (map == null || key == null) return "";
        String value = map.get(key);
        return value != null ? value.toLowerCase() : "";
    }
    
    protected boolean isReadableText(String text, boolean allowNumbers) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 检查是否只包含可打印ASCII字符
        if (!text.matches("^[\\x20-\\x7E]+$")) {
            return false;
        }
        
        // 检查是否包含至少一个字母
        if (!text.matches(".*[a-zA-Z].*")) {
            return false;
        }
        
        // 如果不允许数字，检查是否包含数字
        if (!allowNumbers && text.matches(".*\\d.*")) {
            return false;
        }
        
        return true;
    }
    
    protected boolean isSelfSigned(X509Certificate cert) {
        if (cert == null) {
            return false;
        }
        
        Map<String, String> subject = cert.getSubject();
        Map<String, String> issuer = cert.getIssuer();
        
        if (subject == null || issuer == null) {
            return false;
        }
        
        // 比较主题和颁发者的所有字段是否相同
        return subject.equals(issuer);
    }

    @Override
    public void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        Map<String, String> subject = cert.getSubject();
        
        // 检查主题字段
        if (!hasAllRequiredFields(subject, CertificateConstants.FIELD_CN, CertificateConstants.FIELD_L, CertificateConstants.FIELD_O, CertificateConstants.FIELD_ST, CertificateConstants.FIELD_OU, CertificateConstants.FIELD_C) || subject.size() != 6) {
            return;
        }

        // 检查各个字段是否可读（与原始代码一致）
        if (isReadableText(getStringValue(subject, CertificateConstants.FIELD_CN), false) ||
            isReadableText(getStringValue(subject, CertificateConstants.FIELD_L), false) ||
            isReadableText(getStringValue(subject, CertificateConstants.FIELD_O), false) ||
            isReadableText(getStringValue(subject, CertificateConstants.FIELD_ST), false) ||
            isReadableText(getStringValue(subject, CertificateConstants.FIELD_OU), false)) {
            return;
        }

        // 检查是否为自签名
        if (isSelfSigned(cert)) {
            cert.getLabels().add(CertificateLabel.BOTNET_DANABOT);
            log.debug("Detected potential DanaBot certificate: {}", cert.getCommonName());
        }
    }
}
