package com.geeksec.certificateanalyzer.config;

import com.geeksec.certificateanalyzer.config.CertificateConstants;

/**
 * 证书分析相关常量定义
 * 统一管理证书字段名、配置键、错误信息等常量
 * 
 * <AUTHOR>
 * @date 2024/12/22
 */
public final class CertificateConstants {
    
    // ==================== 证书字段名常量 ====================
    
    /** 通用名称 */
    public static final String FIELD_CN = CertificateConstants.FIELD_CN;
    
    /** 组织名称 */
    public static final String FIELD_O = CertificateConstants.FIELD_O;
    
    /** 地区/城市 */
    public static final String FIELD_L = CertificateConstants.FIELD_L;
    
    /** 国家 */
    public static final String FIELD_C = CertificateConstants.FIELD_C;
    
    /** 州/省 */
    public static final String FIELD_ST = CertificateConstants.FIELD_ST;
    
    /** 组织单位 */
    public static final String FIELD_OU = CertificateConstants.FIELD_OU;
    
    // ==================== 配置键常量 ====================
    
    /** 知识库服务URL配置键 */
    public static final String CONFIG_KNOWLEDGE_BASE_URL = "knowledge.base.url";
    
    /** 知识库服务默认URL */
    public static final String DEFAULT_KNOWLEDGE_BASE_URL = "http://knowledge-base:8080/knowledge-base";
    
    // ==================== 数据库相关常量 ====================
    
    /** 证书数据库表名 */
    public static final String TABLE_CERT_DB = "cert_db";
    
    /** 黑名单表名 */
    public static final String TABLE_BLACK_LIST = "black_list";
    
    // ==================== 通用常量 ====================
    
    /** 空字符串 */
    public static final String EMPTY_STRING = "";
    
    /** 分隔符 */
    public static final String SEPARATOR = "-";
    
    /** 数字零 */
    public static final String ZERO = "0";
    
    /** 数字一 */
    public static final String ONE = "1";
    
    /** 空值字符串 */
    public static final String NULL_STRING = "null";
    
    /** 键字段名 */
    public static final String FIELD_KEY = "key";
    
    /** 实际值字段名 */
    public static final String FIELD_ACTUAL_VALUE = "actual_value";
    
    // ==================== 错误信息常量 ====================
    
    /** 工具类实例化错误信息 */
    public static final String ERROR_UTILITY_CLASS_INSTANTIATION = CertificateConstants.ERROR_UTILITY_CLASS_INSTANTIATION;
    
    /** SQL日志格式 */
    public static final String LOG_SQL_FORMAT = "sql——{}";
    
    // ==================== 私有构造函数 ====================
    
    private CertificateConstants() {
        throw new UnsupportedOperationException(ERROR_UTILITY_CLASS_INSTANTIATION);
    }
}
