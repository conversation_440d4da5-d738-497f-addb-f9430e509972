package com.geeksec.certificateanalyzer.pipeline.correction;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.pipeline.common.outputtags.CorrectionTags;
import com.geeksec.certificateanalyzer.repository.CertificateRepository;
import com.geeksec.certificateanalyzer.util.cert.CertificateCorrectionValidator;
import com.geeksec.certificateanalyzer.util.storage.minio.MinioCertificateClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 处理证书字节负向错误的处理函数
 * 使用MinIO存储证书数据
 *
 * <AUTHOR>
 * @date 2023/7/5
 * @modified hufengkai
 * @date 2024/10/15
 */
@Slf4j
public class ReverseChunkHashCorrector extends ProcessFunction<X509Certificate, X509Certificate> {
    private transient CertificateRepository certificateRepository;
    // private transient JedisPool jedisPool = null; // Removed
    /**
     * Constructor with CertificateRepository dependency.
     *
     * @param certificateRepository The repository for accessing certificate data.
     */
    public ReverseChunkHashCorrector(CertificateRepository certificateRepository) {
        this.certificateRepository = certificateRepository;
    }

    // @Override // open() and close() for JedisPool removed
    // public void open(Configuration parameters) throws Exception { ... }
    // @Override
    // public void close() throws Exception { ... }

    @Override
    public void processElement(X509Certificate originalCert, ProcessFunction<X509Certificate, X509Certificate>.Context context,
            Collector<X509Certificate> collector) throws Exception {
        // 提取证书的负向哈希列表
        List<String> reverseChunkHashes = originalCert.getReverseChunkHashes();
        int length = reverseChunkHashes.size();
        originalCert.setWellFormed(false);

        try {
            String finalCorrectSha1 = this.certificateRepository.findDerSha1ByReverseHashes(reverseChunkHashes, length);
            // String sourceOfSha1 = "Repository"; // Simplified

            if (finalCorrectSha1 != null) {
                log.info("CertificateRepository中查询到反向纠错信息，SHA1: {}", finalCorrectSha1);
                List<String> sha1List = Arrays.asList(finalCorrectSha1.split("_")[0]);
                Map<String, byte[]> resultMap = MinioCertificateClient.batchGetCertificatesBySha1(sha1List);

                if (resultMap != null && !resultMap.isEmpty()) {
                    processCertificateData(originalCert, resultMap, finalCorrectSha1, context, "Repository");
                } else {
                    log.error("MinIO中无法查询到 Repository 中SHA1为 {} 的证书", finalCorrectSha1);
                    originalCert.setWellFormed(false);
                    context.output(CorrectionTags.ReverseChunkHash.FAIL, originalCert);
                }
            } else {
                // CertificateRepository 未命中
                log.warn("CertificateRepository 未查询到证书 {} (reverseChunkHashes: {}) 的反向纠错信息", originalCert.getDerSha1(), reverseChunkHashes);
                originalCert.setWellFormed(false);
                context.output(CorrectionTags.ReverseChunkHash.FAIL, originalCert);
            }
        } catch (Exception e) {
            log.error("处理证书 {} (reverseChunkHashes: {}) 失败: {}", 
                      originalCert.getDerSha1(), reverseChunkHashes, e.getMessage(), e);
            originalCert.setWellFormed(false);
            context.output(CorrectionTags.ReverseChunkHash.FAIL, originalCert);
        }
    }

    /**
     * 处理证书数据
     *
     * @param cert  原始证书
     * @param resultMap 查询结果
     * @param sha1      证书SHA1
     * @param context   处理上下文
     * @param source    数据来源（Redis或ES）
     */
    private void processCertificateData(X509Certificate originalCert, Map<String, byte[]> resultMap,
            String foundSha1InCache, ProcessFunction<X509Certificate, X509Certificate>.Context context, String source) {

        for (Map.Entry<String, byte[]> entry : resultMap.entrySet()) {
            byte[] correctedCertByte = entry.getValue();
            String errorName = "ByteNumNegative"; // Hardcoded as this processor handles negative errors
            X509Certificate correctedCert = new X509Certificate(correctedCertByte);

            log.debug("{} 查询到的证书数据长度: {}", source, correctedCertByte.length);

            // Compare the original certificate's bytes with the corrected one from MinIO
            if (CertificateCorrectionValidator.checkDistance(correctedCertByte, originalCert.getCert(), errorName)) {
                // Parse the content of the corrected certificate
                // 证书内容已在构建 X509Certificate 对象时通过 CertificateParser 解析
                // correctedCert.setDerSha1() is not needed as it's already set during parsing
                correctedCert.setWellFormed(true);
                correctedCert.setCorrupted(false); // Mark as no longer corrupted
                // Add a label to indicate this cert was corrected by reverse chunk hash
                // Note: getLabels() returns List<Integer>, need to add an appropriate label ID
                // correctedCert.getLabels().add(2); // Assuming 2 is the ID for reverse chunk hash corrected certs

                log.info("证书 {} (original SHA1: {}) 纠错成功 using data from {}, corrected SHA1: {}. Outputting corrected certificate.", 
                         originalCert.getDerSha1(), originalCert.getDerSha1(), source, correctedCert.getDerSha1());
                context.output(CorrectionTags.ReverseChunkHash.SUCCESS, correctedCert);
            } else {
                log.error("证书 {} (original SHA1: {}) 纠错失败 using data from {}. LevenshteinDistance过长. Found SHA1 in cache: {}. Outputting original certificate.", 
                          originalCert.getDerSha1(), originalCert.getDerSha1(), source, foundSha1InCache);
                originalCert.setWellFormed(false);
                context.output(CorrectionTags.ReverseChunkHash.FAIL, originalCert);
            }
            break;
        }
    }
}
