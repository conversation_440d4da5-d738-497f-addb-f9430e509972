package com.geeksec.certificateanalyzer.sink.alarm;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.ArrayList;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.List;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Map;

/**
 * 攻击链构建器
 * 专门负责构建攻击链相关的数据
 * 
 * <AUTHOR>
 */
public final class AttackChainBuilder {
    
    /**
     * 私有构造函数，防止实例化
     */
    private AttackChainBuilder() {
        throw new UnsupportedOperationException(CertificateConstants.ERROR_UTILITY_CLASS_INSTANTIATION);
    }
    
    /**
     * 构建攻击链列表
     * 
     * @param victimListInfo 受害者信息列表
     * @param attackerListInfo 攻击者信息列表
     * @param labels 标签列表
     * @param alarmId 告警ID
     * @return 攻击链列表
     */
    public static List<String> buildAttackChainList(List<Map<String, String>> victimListInfo,
                                                   List<Map<String, String>> attackerListInfo,
                                                   List<String> labels, String alarmId) {
        List<String> victims = extractIpList(victimListInfo);
        List<String> attackers = extractIpList(attackerListInfo);
        
        List<String> attackChainList = new ArrayList<>();
        
        for (String victim : victims) {
            for (String attacker : attackers) {
                if (!labels.isEmpty()) {
                    for (String label : labels) {
                        String attackChain = buildAttackChainString(victim, attacker, label);
                        attackChainList.add(attackChain);
                    }
                } else {
                    String attackChain = buildAttackChainString(victim, attacker, alarmId);
                    attackChainList.add(attackChain);
                }
            }
        }
        
        return attackChainList;
    }
    
    /**
     * 从信息列表中提取IP地址列表
     * 
     * @param infoList 信息列表
     * @return IP地址列表
     */
    private static List<String> extractIpList(List<Map<String, String>> infoList) {
        List<String> ipList = new ArrayList<>();
        for (Map<String, String> info : infoList) {
            String ip = info.get("ip");
            if (ip != null) {
                ipList.add(ip);
            }
        }
        return ipList;
    }
    
    /**
     * 构建攻击链字符串
     * 
     * @param victim 受害者
     * @param attacker 攻击者
     * @param identifier 标识符（标签或告警ID）
     * @return 攻击链字符串
     */
    private static String buildAttackChainString(String victim, String attacker, String identifier) {
        return victim + "_" + attacker + "_" + identifier;
    }
}
