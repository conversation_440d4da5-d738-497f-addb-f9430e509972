package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl;

import java.util.Set;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.pipeline.detection.detector.BaseCertificateDetector;

import lombok.extern.slf4j.Slf4j;

/**
 * Tor 相关证书检测器
 */
@Slf4j
public class TorDetector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "Tor Detector";
    
    public TorDetector() {
        super(DETECTOR_NAME);
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        String commonName = cert.getCommonName();
        if (isOnionAddress(commonName)) {
            Set<CertificateLabel> labels = cert.getLabels();
            labels.add(CertificateLabel.THREAT);
            labels.add(CertificateLabel.TOR);
            log.debug("Detected Tor (.onion) address in certificate: {}", commonName);
        }
    }
    
    private boolean isOnionAddress(String domain) {
        return domain != null && domain.endsWith(".onion");
    }
}
