package com.geeksec.certificateanalyzer.pipeline.analysis.signature;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.signature.registry.ServiceRegistry;
import com.geeksec.certificateanalyzer.operator.common.outputtags.ValidationOutputTags;
import com.geeksec.certificateanalyzer.sink.minio.CertificateStorageService;

import lombok.extern.slf4j.Slf4j;

/**
 * 签名验证协调器
 * 负责协调证书签名验证流程，决定证书是否需要进行签名验证
 *
 * <AUTHOR>
 */
@Slf4j
public class SignatureValidationService extends RichMapFunction<X509Certificate, X509Certificate> {

    public static Map<String, String> validationTypeMap = new HashMap<>();

    private CertificateStorageService certificateStorageService;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("签名验证协调器初始化开始");

        // 初始化证书存储服务
        this.certificateStorageService = new CertificateStorageService();

        // 加载验证类型映射配置
        loadValidationTypeMapping();

        log.info("签名验证协调器初始化完成");
    }

    @Override
    public void close() throws Exception {
        super.close();
        log.info("签名验证协调器关闭");
    }

    @Override
    public void processElement(X509Certificate certificate, Context context, Collector<Row> collector) throws Exception {
        try {
            log.debug("处理证书签名验证判断: {}", certificate.getDerSha1());
            
            // 获取证书标签列表
            Set<CertificateLabel> securityLabels = certificate.getLabels();
            if (securityLabels == null) {
                securityLabels = new HashSet<>();
            }
            
            // 创建签名验证行数据
            Row signatureValidationRow = createSignatureValidationRow(certificate);
            
            // 检查是否为自签名证书
            if (isSelfSignedCertificate(certificate, securityLabels)) {
                // 处理自签名证书
                handleSelfSignedCertificate(certificate, securityLabels, signatureValidationRow, context);
            } else {
                // 需要进行签名验证
                log.debug("证书需要进行签名验证: {}", certificate.getDerSha1());
                context.output(ValidationOutputTags.GOTO_VALIDATION, signatureValidationRow);
            }

        } catch (Exception e) {
            log.error("处理证书签名验证判断时发生异常，证书ID: " + certificate.getDerSha1(), e);
            // 异常情况下也要输出数据，避免数据丢失
            Row errorRow = createSignatureValidationRow(certificate);
            context.output(ValidationOutputTags.GOTO_EVALUATION, errorRow);
        }
    }

    /**
     * 加载验证类型映射配置
     *
     * 重构说明：移除了对OID.csv文件的直接依赖，改为使用默认配置
     * TODO: 待knowledge-base服务补充OID配置API后，可从知识库获取OID映射配置
     */
    private void loadValidationTypeMapping() throws IOException {
        try {
            // TODO: 待knowledge-base服务补充OID配置API后，替换以下实现
            // 目前使用默认的验证类型映射配置
            log.info("使用默认的验证类型映射配置");

        } catch (Exception e) {
            log.error("加载验证类型映射配置失败", e);
            throw new IOException("验证类型映射配置加载失败", e);
        }
    }

    /**
     * 创建签名验证行数据
     * 
     * @param certificate 证书对象
     * @return 签名验证行数据
     */
    private Row createSignatureValidationRow(X509Certificate certificate) {
        Row signatureRow = new Row(2);
        signatureRow.setField(0, certificate);
        signatureRow.setField(1, new HashMap<String, X509Certificate>());
        return signatureRow;
    }

    /**
     * 检查是否为自签名证书
     * 
     * @param certificate 证书对象
     * @param labels 标签列表
     * @return 是否为自签名证书
     */
    private boolean isSelfSignedCertificate(X509Certificate certificate, Set<CertificateLabel> labels) {
        // TODO: 实现自签名证书检查逻辑
        // 需要检查证书的标签中是否包含"Self Signed Cert"对应的整型标签
        
        // 临时实现：检查颁发者和主题是否相同
        String issuer = certificate.getIssuer().get("commonName");
        String subject = certificate.getSubject().get("commonName");
        
        boolean isSelfSigned = issuer != null && subject != null && issuer.equals(subject);
        log.debug("证书自签名检查结果: {}, 证书ID: {}", isSelfSigned, certificate.getDerSha1());
        
        return isSelfSigned;
    }

    /**
     * 处理自签名证书
     * 对应nta_2.0中CertIfSign的自签名证书处理逻辑
     * 
     * @param certificate 证书对象
     * @param labels 标签列表
     * @param signatureRow 签名验证行数据
     * @param context 处理上下文
     */
    private void handleSelfSignedCertificate(X509Certificate certificate, Set<CertificateLabel> labels,
                                           Row signatureRow, Context context) {
        log.debug("处理自签名证书: {}", certificate.getDerSha1());
        
        // 检查基本约束扩展
        Map<String, String> extensions = certificate.getExtension();
        if (extensions != null) {
            String basicConstraints = extensions.getOrDefault("basicConstraints", "No basicConstraints");
            if (!basicConstraints.contains("CA:TRUE")) {
                // 添加Unknown CA标签
                labels.add(CertificateLabel.UNKNOWN_CA);
                certificate.setLabels(labels);
                log.debug("为自签名证书添加Unknown CA标签: {}", certificate.getDerSha1());
            }
        }
        
        // 设置父证书ID列表为自身
        Set<String> parentCertIds = new HashSet<>();
        parentCertIds.add(certificate.getDerSha1());
        certificate.setCertificateChain(parentCertIds);
        
        // 更新签名验证行数据
        signatureRow.setField(0, certificate);
        
        // 自签名证书直接跳转到评估流程
        log.debug("自签名证书跳转到评估流程: {}", certificate.getDerSha1());
        context.output(ValidationOutputTags.GOTO_EVALUATION, signatureRow);
    }


}
