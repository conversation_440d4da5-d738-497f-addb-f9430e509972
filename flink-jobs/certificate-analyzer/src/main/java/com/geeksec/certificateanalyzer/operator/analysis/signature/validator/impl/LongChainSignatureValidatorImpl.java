package com.geeksec.certificateanalyzer.pipeline.analysis.signature.validator.impl;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.context.CertificateValidationContext;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.result.ValidationResult;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.validator.LongChainCertificateValidator;
import com.geeksec.certificateanalyzer.pipeline.common.outputtags.ValidationOutputTags;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.util.List;

/**
 * 长链证书签名验证器实现
 * 负责处理超过四级的证书链，直接标记为长链证书
 *
 * 特殊说明：
 * 与其他验证器不同，此验证器不进行实际的签名验证，
 * 而是直接为证书添加"Long CertList"标签并结束验证流程。
 * 这是为了防止过长的证书链导致性能问题。
 * 
 * <AUTHOR>
 */
@Slf4j
public class LongChainSignatureValidatorImpl extends AbstractCertificateValidatorImpl 
        implements LongChainCertificateValidator {
    
    private static final int DEFAULT_CHAIN_LENGTH_THRESHOLD = 4;

    public LongChainSignatureValidatorImpl() {
        super(ValidationLevel.LEVEL_5);
    }
    
    @Override
    public ValidationResult validateLongChain(CertificateValidationContext context) {
        try {
            log.debug("长链签名验证处理证书: {}", context.getCertificate().getDerSha1());
            
            // 检查证书链是否过长
            if (isChainTooLong(context.getParentCertificateIds(), DEFAULT_CHAIN_LENGTH_THRESHOLD)) {
                log.debug("检测到长证书链，添加Long CertList标签: {}", context.getCertificate().getDerSha1());
                
                // 添加长链标签
                context.getSecurityLabels().add(CertificateLabel.LONG_CERT_LIST);
                
                // 更新证书信息
                context.getCertificate().setLabels(context.getSecurityLabels());
                context.getCertificate().setParentCertIdList(context.getParentCertificateIds());
                
                // 创建输出行数据
                Row outputRow = createOutputRow(context.getCertificate(), context.getParentCertificateMap());
                
                log.debug("长链证书处理完成: {}", context.getCertificate().getDerSha1());
                return ValidationResult.stopValidation(outputRow, "长链证书验证完成");
            } else {
                log.warn("长链验证器接收到非长链证书: {}, 链长度: {}", 
                        context.getCertificate().getDerSha1(), 
                        context.getParentCertificateIds().size());
                
                // 创建输出行数据
                Row outputRow = createOutputRow(context.getCertificate(), context.getParentCertificateMap());
                return ValidationResult.stopValidation(outputRow, "非长链证书");
            }
            
        } catch (Exception e) {
            log.error("长链签名验证处理证书时发生异常", e);
            Row errorRow = createOutputRow(context.getCertificate(), context.getParentCertificateMap());
            return ValidationResult.stopValidation(errorRow, "验证异常");
        }
    }
    
    @Override
    public boolean isChainTooLong(List<String> parentCertificateIds, int threshold) {
        if (parentCertificateIds == null) {
            return false;
        }
        return parentCertificateIds.size() > threshold;
    }
    
    @Override
    protected void handleValidationResult(ValidationResult result, Context ctx) {
        try {
            switch (result.getAction()) {
                case STOP_VALIDATION:
                    ctx.output(ValidationOutputTags.STOP_VALIDATION_5, result.getOutputRow());
                    break;
                case GOTO_EVALUATION:
                    ctx.output(ValidationOutputTags.GOTO_EVALUATION, result.getOutputRow());
                    break;
                case CONTINUE_VALIDATION:
                    // 长链验证器通常不会继续验证，但为了完整性保留此分支
                    log.warn("长链验证器不应该继续验证，将其转为停止验证");
                    ctx.output(ValidationOutputTags.STOP_VALIDATION_5, result.getOutputRow());
                    break;
                default:
                    log.warn("未知的验证动作: {}", result.getAction());
                    ctx.output(ValidationOutputTags.STOP_VALIDATION_5, result.getOutputRow());
                    break;
            }
        } catch (Exception e) {
            log.error("处理验证结果时发生异常", e);
        }
    }
}
