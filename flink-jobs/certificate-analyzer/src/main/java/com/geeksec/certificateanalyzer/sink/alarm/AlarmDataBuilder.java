package com.geeksec.certificateanalyzer.sink.alarm;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.ArrayList;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.HashMap;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.List;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Map;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import org.apache.flink.types.Row;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书告警数据构建器
 * 专门负责构建告警相关的数据结构
 * 
 * <AUTHOR>
 */
@Slf4j
public final class AlarmDataBuilder {
    
    /**
     * 私有构造函数，防止实例化
     */
    private AlarmDataBuilder() {
        throw new UnsupportedOperationException(CertificateConstants.ERROR_UTILITY_CLASS_INSTANTIATION);
    }
    
    /**
     * 获取默认的告警信息映射
     * 
     * @return 包含默认告警字段的Map
     */
    public static Map<String, Object> getDefaultAlarmMap() {
        Map<String, Object> defaultAlarmMap = new HashMap<>();
        defaultAlarmMap.put("alarm_reason", new ArrayList<>());
        defaultAlarmMap.put("alarm_status", 0); // 0:未处理 1:确认 2:误报
        defaultAlarmMap.put("alarm_type", new ArrayList<>()); // 防御/模型/规则
        defaultAlarmMap.put("targets", new ArrayList<>());
        defaultAlarmMap.put("victim", new ArrayList<>());
        defaultAlarmMap.put("attacker", new ArrayList<>());
        defaultAlarmMap.put("attack_family", new ArrayList<>());
        defaultAlarmMap.put("ioc", new ArrayList<>());
        return defaultAlarmMap;
    }
    
    /**
     * 构建发送数据
     * 
     * @param alarmData 告警数据
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 发送数据Map
     */
    public static Map<String, Object> buildSendData(Map<String, Object> alarmData, String taskId, String batchId) {
        Map<String, Object> sendData = new HashMap<>();
        sendData.put("TaskId", taskId);
        sendData.put("BatchId", batchId);
        alarmData.put("task_id", Integer.valueOf(taskId));
        sendData.put("Alarm", alarmData);
        return sendData;
    }
    
    /**
     * 构建已知告警信息
     * 
     * @param infoRow 信息行
     * @param alarmInfoMap 告警信息映射
     * @return 已知告警信息Map
     */
    public static Map<String, Object> buildKnownAlarmInfo(Row infoRow, Map<String, Map<String, String>> alarmInfoMap) {
        String alarmType = infoRow.getFieldAs(0);
        Map<String, Object> knownInfo = new HashMap<>();
        Map<String, String> alarmKnownData = alarmInfoMap.get(alarmType);
        
        if (alarmKnownData != null) {
            knownInfo.put("alarm_knowledge_id", alarmKnownData.get("alarm_knowledge_id"));
            knownInfo.put("attack_level", alarmKnownData.get("attack_level"));
            knownInfo.put("alarm_name", alarmKnownData.get("alarm_name"));
            knownInfo.put("alarm_principle", alarmKnownData.get("alarm_principle"));
            knownInfo.put("attack_chain_name", alarmKnownData.get("attack_chain_name"));
        }
        
        long currentTime = System.currentTimeMillis();
        int currentTimeSeconds = (int) (currentTime / 1000);
        knownInfo.put("time", currentTimeSeconds);
        
        return knownInfo;
    }
    
    /**
     * 创建标签边的行记录
     * 
     * @param src 源节点
     * @param dst 目标节点
     * @return 包含标签边的行记录
     */
    public static Row createLabelEdgeRow(Object src, Object dst) {
        Row resultRow = new Row(5);
        resultRow.setField(0, src);
        resultRow.setField(1, dst);
        resultRow.setField(2, 0);
        resultRow.setField(3, "Add_Label");
        resultRow.setField(4, "");
        return resultRow;
    }
}
