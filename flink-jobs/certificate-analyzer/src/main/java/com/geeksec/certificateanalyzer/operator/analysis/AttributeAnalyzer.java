package com.geeksec.certificateanalyzer.operator.analysis;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.io.IOException;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.time.temporal.ChronoUnit;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.HashMap;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.HashSet;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.List;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Map;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Set;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import org.apache.commons.validator.routines.EmailValidator;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import org.apache.flink.api.common.functions.RichMapFunction;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.util.cert.CertificateNameParser;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.common.infrastructure.network.NetworkUtils;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.common.toolkit.time.TimeUtils;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书详细分析器
 * 负责进行详细的证书分析，包括EV/DV/OV检测、SAN分析、有效期分析、域名分析等
 * 
 * <AUTHOR>
 * @date 2024/12/22
 */
@Slf4j
public class AttributeAnalyzer extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 知识库客户端 */
    private KnowledgeBaseClient knowledgeBaseClient;
    
    /** Tranco Top域名列表 */
    private Set<String> trancoTopDomainList = new HashSet<>();

    /** Tranco Top域名映射 */
    private Map<String, Integer> trancoTopDomainMap = new HashMap<>();
    
    /** CDN名称列表 */
    private Set<String> cdnNameList = new HashSet<>();
    
    /** 视频网站列表 */
    private Set<String> videoWebList = new HashSet<>();
    
    /** EV证书策略OID列表 */
    private Set<String> evPolicyOids = new HashSet<>();
    
    /** DV证书策略OID列表 */
    private Set<String> dvPolicyOids = new HashSet<>();
    
    /** OV证书策略OID列表 */
    private Set<String> ovPolicyOids = new HashSet<>();
    
    /** Windows信任根证书SHA1列表 */
    private static final Set<String> WINDOWS_TRUST_CERTS = Set.of(
        "06f1aa330b927b753a40e68cdf22e34bcbef3352",
        "31f9fc8ba3805986b721ea7295c65b3a44534274",
        "0119e81be9a14cd8e22f40ac118c687ecba3f4d8",
        "0563b8630d62d75abbc8ab1e4bdfb5a899b24d43"
    );
    
    /** 一年的秒数 */
    private static final long SECONDS_PER_YEAR = 86400L * 366;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化知识库客户端
        knowledgeBaseClient = new KnowledgeBaseClient();
        
        // 加载详细分析所需的数据
        loadDetailedAnalysisData();
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("进行证书详细分析，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 执行各种详细分析
        analyzeWindowsTrust(certificate, labels);
        analyzeCertificateType(certificate, labels);
        analyzeValidityPeriod(certificate, labels);
        analyzeSanValues(certificate, labels);
        analyzeSubjectAndIssuer(certificate, labels);
        analyzeCertificateValidationType(certificate, labels);
        analyzeDomainFeatures(certificate, labels);
        analyzeSpecialFeatures(certificate, labels);

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 加载详细分析所需的数据
     */
    private void loadDetailedAnalysisData() throws IOException {
        try {
            // 从知识库服务加载各种配置数据
            trancoTopDomainList = knowledgeBaseClient.getTrancoTopDomains();
            trancoTopDomainMap = knowledgeBaseClient.getTrancoTopDomainMap();
            cdnNameList = knowledgeBaseClient.getCdnNames();
            videoWebList = knowledgeBaseClient.getVideoWebsites();
            evPolicyOids = knowledgeBaseClient.getEvPolicyOids();
            dvPolicyOids = knowledgeBaseClient.getDvPolicyOids();
            ovPolicyOids = knowledgeBaseClient.getOvPolicyOids();

            log.info("成功从知识库加载详细分析数据: Tranco域名{}, CDN{}, 视频网站{}, EV策略{}, DV策略{}, OV策略{}",
                    trancoTopDomainList.size(), cdnNameList.size(), videoWebList.size(),
                    evPolicyOids.size(), dvPolicyOids.size(), ovPolicyOids.size());
                    
        } catch (Exception e) {
            log.error("从知识库加载详细分析数据失败，使用默认值", e);
            // 使用默认的空集合
            trancoTopDomainList = new HashSet<>();
            trancoTopDomainMap = new HashMap<>();
            cdnNameList = new HashSet<>();
            videoWebList = new HashSet<>();
            evPolicyOids = new HashSet<>();
            dvPolicyOids = new HashSet<>();
            ovPolicyOids = new HashSet<>();
            throw new IOException("从知识库加载详细分析数据失败", e);
        }
    }

    /**
     * 分析Windows信任根证书
     */
    private void analyzeWindowsTrust(X509Certificate certificate, Set<CertificateLabel> labels) {
        String sha1 = certificate.getDerSha1();
        if (sha1 != null && WINDOWS_TRUST_CERTS.contains(sha1.toLowerCase())) {
            labels.add(CertificateLabel.WINDOWS_TRUST);
            labels.add(CertificateLabel.CENTOS_TRUST);
            labels.add(CertificateLabel.APPLE_TRUST);
        }
    }

    /**
     * 分析证书类型
     */
    private void analyzeCertificateType(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 获取基本约束扩展
        String basicConstraints = getExtensionValue(certificate, "basicConstraints");
        String keyUsage = certificate.getKeyUsage();
        String extendedKeyUsage = certificate.getExtendedKeyUsage();
        
        if (!basicConstraints.contains("CA:TRUE")) {
            labels.add(CertificateLabel.USER_CERT);
            labels.add(CertificateLabel.LEAF_CERT);
            if (keyUsage != null && !keyUsage.isEmpty() && 
                extendedKeyUsage != null && !extendedKeyUsage.isEmpty()) {
                labels.add(CertificateLabel.APP_LEAF_CERT);
            }
        } else {
            // 检查是否为根证书（自签名）
            if (certificate.getSubjectId() != null && 
                certificate.getSubjectId().equals(certificate.getIssuerId())) {
                labels.add(CertificateLabel.PRIVATE_CA);
                labels.add(CertificateLabel.ROOT_CA);
            } else {
                labels.add(CertificateLabel.PRIVATE_CA);
                labels.add(CertificateLabel.CA_CERT);
            }
        }
    }

    /**
     * 分析证书有效期
     */
    private void analyzeValidityPeriod(X509Certificate certificate, Set<CertificateLabel> labels) {
        if (certificate.getNotBefore() != null && certificate.getNotAfter() != null) {
            long durationSeconds = ChronoUnit.SECONDS.between(
                certificate.getNotBefore(), certificate.getNotAfter());
            
            // 短期证书（少于90天）
            if (durationSeconds < 86400L * 90) {
                labels.add(CertificateLabel.SHORT_VALIDITY_CERT);
            }
            
            // 长期证书（超过2年）
            if (durationSeconds > SECONDS_PER_YEAR * 2) {
                labels.add(CertificateLabel.LONG_DURATION_CERT);
            }
            
            // 检查是否过期
            if (certificate.getNotAfter().isBefore(TimeUtils.now())) {
                labels.add(CertificateLabel.EXPIRED);
            } else {
                labels.add(CertificateLabel.VALID);
            }
        }
    }

    /**
     * 分析SAN值
     */
    private void analyzeSanValues(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> sanList = certificate.getSubjectAltNames();
        if (sanList == null || sanList.isEmpty()) {
            return;
        }
        
        for (String san : sanList) {
            // 检查IP地址
            if (NetworkUtils.isValidIp(san)) {
                labels.add(CertificateLabel.IP_IN_SAN);
            }
            
            // 检查通配符
            if (san.startsWith("*.")) {
                labels.add(CertificateLabel.WILDCARD_IN_SAN);
            }
            
            // 检查邮箱
            if (EmailValidator.getInstance().isValid(san)) {
                labels.add(CertificateLabel.EMAIL_IN_SAN);
            }
            
            // 检查URI
            if (san.startsWith("http://") || san.startsWith("https://") || san.startsWith("ftp://")) {
                labels.add(CertificateLabel.URI_IN_SAN);
            }
        }
    }

    /**
     * 分析主题和颁发者
     */
    private void analyzeSubjectAndIssuer(X509Certificate certificate, Set<CertificateLabel> labels) {
        Map<String, Object> issuerInfo = CertificateNameParser.parse(certificate.getIssuer());
        String issuerO = issuerInfo.getOrDefault(CertificateConstants.FIELD_O, "").toString();
        String issuerCN = issuerInfo.getOrDefault(CertificateConstants.FIELD_CN, "").toString();
        
        // 检查颁发者中的通配符
        if (issuerO.contains("*") || issuerCN.contains("*")) {
            labels.add(CertificateLabel.WILDCARD_IN_ISSUER);
        }
    }

    /**
     * 分析证书验证类型（EV/DV/OV）
     */
    private void analyzeCertificateValidationType(X509Certificate certificate, Set<CertificateLabel> labels) {
        String issuerStr = certificate.getIssuer().toLowerCase();
        Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());
        String subjectOU = subjectInfo.getOrDefault(CertificateConstants.FIELD_OU, "").toString();
        
        // 获取证书策略
        String certificatePolicies = getExtensionValue(certificate, "certificatePolicies");
        Set<String> policyOids = extractPolicyOids(certificatePolicies);
        
        // EV证书检测
        if (issuerStr.contains("ev") || issuerStr.contains("extended validation") ||
            issuerStr.contains("serialnumber") || containsAnyOid(policyOids, evPolicyOids)) {
            labels.add(CertificateLabel.EV_CERT);
        }
        // DV证书检测
        else if (issuerStr.contains("dv") || issuerStr.contains("domain validation") ||
                 subjectOU.contains("Domain Control") || containsAnyOid(policyOids, dvPolicyOids)) {
            labels.add(CertificateLabel.DV_CERT);
        }
        // OV证书检测
        else if (issuerStr.contains("ov") || issuerStr.contains("organization validation") ||
                 containsAnyOid(policyOids, ovPolicyOids)) {
            labels.add(CertificateLabel.OV_CERT);
        }
    }

    /**
     * 分析域名特征
     */
    private void analyzeDomainFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        if (domains == null || domains.isEmpty()) {
            return;
        }
        
        for (String domain : domains) {
            // 清理域名（移除通配符前缀）
            String cleanDomain = domain.startsWith("*.") ? domain.substring(2) : domain;
            
            // 检查是否为热门域名
            if (trancoTopDomainList.contains(cleanDomain)) {
                labels.add(CertificateLabel.TRANCO_TOP_DOMAIN);
                labels.add(CertificateLabel.HOT_DOMAIN);
            }
            
            // 检查是否为CDN
            for (String cdnName : cdnNameList) {
                if (cleanDomain.contains(cdnName)) {
                    labels.add(CertificateLabel.CDN_CERT);
                    break;
                }
            }
            
            // 检查是否为视频网站
            for (String videoSite : videoWebList) {
                if (cleanDomain.contains(videoSite)) {
                    // 可以添加视频网站相关标签
                    break;
                }
            }
        }
    }

    /**
     * 分析特殊特征
     */
    private void analyzeSpecialFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        String keyUsage = certificate.getKeyUsage();
        String extendedKeyUsage = certificate.getExtendedKeyUsage();
        
        // 分析密钥用途
        if (extendedKeyUsage != null) {
            if (extendedKeyUsage.contains("Code Signing")) {
                labels.add(CertificateLabel.CODE_SIGNING_CERT);
            }
            if (extendedKeyUsage.contains("Email Protection")) {
                labels.add(CertificateLabel.EMAIL_CERT);
            }
            if (extendedKeyUsage.contains("Server Authentication")) {
                labels.add(CertificateLabel.SERVER_AUTH_CERT);
            }
            if (extendedKeyUsage.contains("Client Authentication")) {
                labels.add(CertificateLabel.CLIENT_AUTH_CERT);
            }
        }
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }

    // 辅助方法
    private String getExtensionValue(X509Certificate certificate, String extensionName) {
        if (certificate.getCertificateExtensions() != null && 
            certificate.getCertificateExtensions().getExtensionMap() != null) {
            Object value = certificate.getCertificateExtensions().getExtensionMap().get(extensionName);
            return value != null ? value.toString() : "";
        }
        return "";
    }

    private Set<String> extractPolicyOids(String certificatePolicies) {
        Set<String> oids = new HashSet<>();
        if (certificatePolicies != null && !certificatePolicies.isEmpty()) {
            // 简单的OID提取逻辑，实际实现可能需要更复杂的解析
            String[] parts = certificatePolicies.split(",");
            for (String part : parts) {
                String trimmed = part.trim();
                if (trimmed.matches("\\d+(\\.\\d+)*")) {
                    oids.add(trimmed);
                }
            }
        }
        return oids;
    }

    private boolean containsAnyOid(Set<String> policyOids, Set<String> targetOids) {
        for (String oid : policyOids) {
            if (targetOids.contains(oid)) {
                return true;
            }
        }
        return false;
    }
}
