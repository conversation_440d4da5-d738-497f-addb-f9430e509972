package com.geeksec.certificateanalyzer.pipeline.analysis.signature.factory;

import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.CertificateValidator;

/**
 * 证书验证器工厂接口
 * 负责创建不同类型的证书验证器
 * 
 * <AUTHOR>
 */
public interface CertificateValidatorFactory {
    
    /**
     * 根据验证级别创建验证器
     * 
     * @param level 验证级别
     * @return 证书验证器
     */
    CertificateValidator createValidator(CertificateValidator.ValidationLevel level);
    
    /**
     * 获取所有支持的验证级别
     * 
     * @return 验证级别数组
     */
    CertificateValidator.ValidationLevel[] getSupportedLevels();
}
