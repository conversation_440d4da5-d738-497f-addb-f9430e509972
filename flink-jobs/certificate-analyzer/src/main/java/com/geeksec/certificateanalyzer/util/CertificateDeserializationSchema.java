package com.geeksec.certificateanalyzer.util;

import com.geeksec.certificateanalyzer.enums.CertificateSource;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.model.cert.X509CertificateExtension;
import com.geeksec.certificateanalyzer.util.cert.CertificateUtils;
import com.geeksec.certificateanalyzer.util.data.CertificateDataReader;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateEncodingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * 证书反序列化Schema
 * <p>
 * 负责将从 Kafka 中消费的原始字节数据反序列化为 {@link X509Certificate} 对象。
 * 此类整合了证书解析、字段填充和初步的数据处理逻辑，是数据进入 Flink 处理管道的入口点。
 *
 * <AUTHOR>
 * <AUTHOR> (重构)
 * @date 2022/6/13
 */
@Slf4j
public class CertificateDeserializationSchema implements DeserializationSchema<X509Certificate> {

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<X509Certificate> out) throws IOException {
        X509Certificate customCert = new X509Certificate();
        setKafkaSource(customCert, record);

        try {
            java.security.cert.X509Certificate parsedCert = CertificateUtils.parseCertificate(record.value());
            if (parsedCert == null) {
                throw new java.security.cert.CertificateException("CertificateUtils返回了null");
            }

            // 填充所有字段
            populateCertificateFields(customCert, parsedCert);

            out.collect(customCert);

        } catch (Exception e) {
            log.error("证书解析或处理失败, Topic: {}, Partition: {}, Offset: {}. Error: {}",
                    record.topic(), record.partition(), record.offset(), e.getMessage());
            // 对于解析失败的证书，进行标记或记录
            handleDeserializationError(record.value(), customCert, out, e);
        }
    }

    /**
     * 填充证书模型的各个字段。
     *
     * @param customCert 自定义的证书模型对象
     * @param parsedCert Java标准库的证书对象
     * @throws CertificateEncodingException 如果证书编码出错
     */
    private void populateCertificateFields(X509Certificate customCert, java.security.cert.X509Certificate parsedCert) throws CertificateEncodingException {
        byte[] encodedCert = parsedCert.getEncoded();

        // 基础信息
        customCert.setCert(encodedCert);
        customCert.setVersion(String.valueOf(parsedCert.getVersion()));
        customCert.setSerialNumber(parsedCert.getSerialNumber().toString(16));
        customCert.setFormat(parsedCert.getType());

        // 时间信息
        customCert.setNotBefore(parsedCert.getNotBefore().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        customCert.setNotAfter(parsedCert.getNotAfter().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        customCert.setDuration(parsedCert.getNotAfter().getTime() - parsedCert.getNotBefore().getTime());
        customCert.setImportTime(java.time.LocalDateTime.now());

        // 名称信息
        customCert.setSubject(CertificateUtils.parseSubject(parsedCert.getSubjectX500Principal().getName()));
        customCert.setIssuer(CertificateUtils.parseIssuer(parsedCert.getIssuerX500Principal().getName()));
        customCert.setCommonName(customCert.getSubject().getOrDefault("CN", ""));

        // 哈希指纹
        customCert.setDerSha1(CertificateUtils.getSha1Fingerprint(parsedCert));
        customCert.setDerMd5(CertificateUtils.getMd5Fingerprint(parsedCert));
        customCert.setDerSha256(CertificateUtils.getSha256Fingerprint(parsedCert));

        // 公钥信息
        customCert.setPublicKey(CertificateUtils.bytesToHex(parsedCert.getPublicKey().getEncoded()));
        customCert.setPublicKeyAlgorithm(parsedCert.getPublicKey().getAlgorithm());

        // 签名信息
        customCert.setSignature(CertificateUtils.bytesToHex(parsedCert.getSignature()));
        customCert.setSignatureAlgorithm(parsedCert.getSigAlgName());

        // 扩展信息
        X509CertificateExtension extensions = new X509CertificateExtension(parsedCert);
        customCert.setExtensions(extensions);

        // 初始化评分
        customCert.setThreatScore(0);
        customCert.setTrustScore(0);
    }

    /**
     * 处理反序列化过程中发生的错误。
     *
     * @param originalData 原始证书数据
     * @param cert         待填充的证书模型
     * @param collector    Flink的收集器
     * @param ex           捕获到的异常
     */
    private void handleDeserializationError(byte[] originalData, X509Certificate cert, Collector<X509Certificate> collector, Exception ex) {
        log.warn("无法解析证书，将仅保存原始数据和哈希值。Error: {}", ex.getMessage());
        cert.setCert(originalData);
        // 即使证书无效，也尝试计算其哈希值
        cert.setDerMd5(CertificateUtils.getMd5Fingerprint(originalData));
        cert.setDerSha1(CertificateUtils.getSha1Fingerprint(originalData));
        cert.setDerSha256(CertificateUtils.getSha256Fingerprint(originalData));
        cert.setImportTime(java.time.LocalDateTime.now());
        collector.collect(cert);
    }

    @Override
    public TypeInformation<X509Certificate> getProducedType() {
        return TypeInformation.of(X509Certificate.class);
    }

    /**
     * 从Kafka记录中提取来源信息并设置到证书对象中。
     */
    private static void setKafkaSource(X509Certificate cert, ConsumerRecord<byte[], byte[]> record) {
        String sourceType = record.topic();
        cert.setSource(CertificateSource.fromKafkaTopic(sourceType));
    }
}
