package com.geeksec.certificateanalyzer.pipeline.analysis.signature.service;

import java.security.cert.CertPathValidatorException;
import java.util.List;

import org.bouncycastle.asn1.x509.Extension;

import com.geeksec.certificateanalyzer.enums.CertificateTrustStatus;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书链验证服务
 * 
 * <AUTHOR>
 */
@Slf4j
public class CertificateChainValidationService {

    public CertificateChainValidationService() {
        // 构造函数保留，但不再需要初始化TrustedCertificateService
    }

    /**
     * 验证证书链
     * 
     * @param certificate 待验证的证书
     * @param certificateChain 证书链
     * @return 证书链是否有效
     */
    public boolean validateCertificateChain(X509Certificate certificate, List<X509Certificate> certificateChain) {
        // 如果证书链为空，则无法验证
        if (certificateChain == null || certificateChain.isEmpty()) {
            return false;
        }

        try {
            // 检查证书链中的每个证书是否有效
            for (X509Certificate certInChain : certificateChain) {
                certInChain.getCert().checkValidity();
            }

            // 直接根据证书自身的信任状态进行判断
            if (certificate.getTrustStatus() == CertificateTrustStatus.TRUSTED) {
                log.info("证书 {} 的证书链验证通过，因为它是受信任的根证书。", certificate.getDerSha1());
                return true;
            }

            // 验证证书链中的签名关系
            for (int i = 0; i < certificateChain.size() - 1; i++) {
                X509Certificate subjectCert = certificateChain.get(i);
                X509Certificate issuerCert = certificateChain.get(i + 1);
                subjectCert.getCert().verify(issuerCert.getCert().getPublicKey());
            }

            // 验证证书链的最后一个证书是否是自签名，或者是否受信任
            X509Certificate rootCert = certificateChain.get(certificateChain.size() - 1);
            if (isSelfSigned(rootCert) || rootCert.getTrustStatus() == CertificateTrustStatus.TRUSTED) {
                log.info("证书 {} 的证书链验证通过。", certificate.getDerSha1());
                return true;
            }

        } catch (Exception e) {
            log.error("证书 {} 的证书链验证失败: {}", certificate.getDerSha1(), e.getMessage());
            return false;
        }

        return false;
    }

    /**
     * 检查证书是否是自签名
     * 
     * @param certificate 待检查的证书
     * @return 如果证书是自签名，则返回true，否则返回false
     */
    private boolean isSelfSigned(X509Certificate certificate) {
        return certificate.getSubject().equals(certificate.getIssuer());
    }
}
