package com.geeksec.certificateanalyzer.alarm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.types.Row;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.alarm.AlarmDataBuilder;
import com.geeksec.common.utils.alarm.AlarmUtils;

/**
 * 证书知识库碰撞结果告警写入器
 *
 * <AUTHOR>
 * @date 2024/9/26
 */
public class KnowledgeBaseAlarmSink extends RichSinkFunction<Alarm> {

    /**
     * APT结果键
     */
    private static final String APT_RESULT_KEY = "apt_result";

    /**
     * 威胁结果键
     */
    private static final String THREAT_RESULT_KEY = "threat_result";

    /**
     * 获取证书告警JSON
     *
     * @param infoRow 信息行
     * @return 告警JSON对象
     */
    public static JSONObject getUseCertAlarmJson(Row infoRow) {
        String alarmType = infoRow.getFieldAs(0);
        if (!CertificateAlarmTransformer.alarmInfoMap.keySet().contains(alarmType)) {
            return null;
        }
        X509Certificate cert = infoRow.getFieldAs(1);
        Map<String, Object> useThreatCertAlarmJson = AlarmDataBuilder.buildKnownAlarmInfo(infoRow, AlarmTransformer.alarmInfoMap);
        useThreatCertAlarmJson.putAll(AlarmDataBuilder.getDefaultAlarmMap());

        // reason
        List<Map<String, Object>> alarmReason = getAlarmReason(cert, alarmType);

        List<Map<String, Object>> targets = getTargets(cert);

        List<Integer> alarmRelatedLabels = getAlarmRelatedLabels(cert);

        // victim
        List<Map<String, String>> victim = get_victim_attacker(cert, alarmType);
        // attacker
        List<Map<String, String>> attacker = get_victim_attacker(cert, alarmType);

        useThreatCertAlarmJson.put("alarm_reason", alarmReason);
        useThreatCertAlarmJson.put("attack_family", new ArrayList<>());
        useThreatCertAlarmJson.put("targets", targets);
        useThreatCertAlarmJson.put("alarm_handle_method",
                "根据知识库碰撞或者证书解析识别出来了证书的信息，以及包括该证书的流量数据的审查过滤，排除该证书关联的流量以及潜在攻击者的威胁");
        useThreatCertAlarmJson.put("alarm_type", "模型");
        useThreatCertAlarmJson.put("victim", victim);
        useThreatCertAlarmJson.put("attacker", attacker);
        useThreatCertAlarmJson.put("alarm_related_label", alarmRelatedLabels);
        useThreatCertAlarmJson.put("attack_route", new ArrayList<>());
        useThreatCertAlarmJson.put("alarm_session_list", new ArrayList<>());
        useThreatCertAlarmJson.put("attack_chain_list", new ArrayList<>());

        // 注意：这里需要提供taskId和batchId，应该从上下文获取
        // 暂时使用默认值，实际使用时需要传入正确的值
        Map<String, Object> sendData = AlarmUtils.getSendData(useThreatCertAlarmJson, "0", "0");
        JSONObject alarmJson = new JSONObject();
        alarmJson.putAll(sendData);
        return alarmJson;
    }

    private static Set<Integer> getAlarmRelatedLabels(X509Certificate cert) {
        return cert.getLabels();
    }

    private static List<Map<String, Object>> getAlarmReason(X509Certificate cert, String alarmType) {
        Map<String, Object> alarmReasonTool = new HashMap<>();
        List<Map<String, Object>> alarmReasonList = new ArrayList<>();
        List<String> aptInfo = new ArrayList<>();
        List<String> threatInfo = new ArrayList<>();
        for (Map<String, List<String>> result : cert.getKnowledgeCollisionResult()) {
            if (result.containsKey(APT_RESULT_KEY)) {
                aptInfo = result.get(APT_RESULT_KEY);
            }
            if (result.containsKey(THREAT_RESULT_KEY)) {
                threatInfo = result.get(THREAT_RESULT_KEY);
            }
        }
        switch (alarmType) {
            case "APT证书碰撞":
                alarmReasonTool.put("key", "与APT证书知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书的APT组织碰撞结果为：" + aptInfo);
                alarmReasonList.add(alarmReasonTool);
                break;
            case "威胁证书碰撞":
                alarmReasonTool.put("key", "与威胁证书知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书的威胁类型碰撞结果为：" + threatInfo);
                alarmReasonList.add(alarmReasonTool);
                break;
            case "失陷IP关联证书":
                alarmReasonTool.put("key", "与失陷IP第三方知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书关联的失陷IP为：" + cert.getIocIP());
                alarmReasonList.add(alarmReasonTool);
                break;
            case "失陷域名关联证书":
                alarmReasonTool.put("key", "与恶意域名第三方知识库碰撞成功");
                alarmReasonTool.put("actual_value", "该证书关联的恶意域名为：" + cert.getIocDomain());
                alarmReasonList.add(alarmReasonTool);
                break;
            case "APT证书上线":
                alarmReasonTool.put("key", "识别到APT组织证书特征");
                alarmReasonTool.put("actual_value", "该证书可能存在以下关联的APT组织：");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "翻墙行为":
                alarmReasonTool.put("key", "识别到翻墙行为证书特征");
                alarmReasonTool.put("actual_value", "该证书使用者及签发者存在翻墙证书相关特征字段，以及证书关联IP域名包含翻墙行为节点属性");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "TOR网络访问":
                alarmReasonTool.put("key", "识别到TOR网络访问证书特征");
                alarmReasonTool.put("actual_value", "该证书关联的域名包含Tor证书的强域名特征");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "C2证书请求":
                alarmReasonTool.put("key", "识别到C2请求证书特征");
                alarmReasonTool.put("actual_value", "该证书使用者及签发者以及使用者可用名，签发者可用名存在C2证书相关特征字段，以及证书关联IP或者域名包含C2服务器节点属性");
                alarmReasonList.add(alarmReasonTool);
                break;
            case "非法挖矿请求":
                alarmReasonTool.put("key", "识别到非法挖矿请求证书特征");
                alarmReasonTool.put("actual_value", "该证书使用者存在矿池相关的域名IP特征");
                alarmReasonList.add(alarmReasonTool);
                break;
            default:
                break;
        }
        return alarmReasonList;
    }

    private static List<Map<String, Object>> getTargets(X509Certificate cert) {
        List<Map<String, Object>> targets = new ArrayList<>();
        Map<String, Object> target_tmp = new HashMap<>();
        target_tmp.put("name", cert.getCertId());
        target_tmp.put("type", "cert");
        List<String> labelsCert = cert.getLabels();
        target_tmp.put("labels", labelsCert);
        targets.add(target_tmp);
        return targets;
    }

    private static List<Map<String, String>> get_victim_attacker(X509Certificate cert, String type) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> item = new HashMap<>();
        switch (type) {
            case "失陷IP关联证书":
                item.put("ip", cert.getIocIP());
                result.add(item);
            case "失陷域名关联证书":
                item.put("domain", cert.getIocIP());
                result.add(item);
            default:
                break;
        }
        return result;
    }
}
