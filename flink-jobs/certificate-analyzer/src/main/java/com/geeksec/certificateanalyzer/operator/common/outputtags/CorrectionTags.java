package com.geeksec.certificateanalyzer.operator.common.outputtags;

import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * 证书纠错输出标签定义
 * 用于Flink流处理中的证书纠错侧输出流标记
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class CorrectionTags {

    /**
     * 正向块哈希纠错输出标签
     */
    public static final OutputTag<Row> FORWARD_CHUNK_HASH_CORRECTION_TAG =
            new OutputTag<Row>("forward-chunk-hash-correction") {};

    /**
     * 反向块哈希纠错输出标签
     */
    public static final OutputTag<Row> REVERSE_CHUNK_HASH_CORRECTION_TAG =
            new OutputTag<Row>("reverse-chunk-hash-correction") {};

    /**
     * 字节序列反向纠错输出标签
     */
    public static final OutputTag<Row> BYTE_SEQUENCE_REVERSE_CORRECTION_TAG =
            new OutputTag<Row>("byte-sequence-reverse-correction") {};

    /**
     * 纠错成功输出标签
     */
    public static final OutputTag<Row> CORRECTION_SUCCESS_TAG =
            new OutputTag<Row>("correction-success") {};

    /**
     * 纠错失败输出标签
     */
    public static final OutputTag<Row> CORRECTION_FAILED_TAG =
            new OutputTag<Row>("correction-failed") {};

    /**
     * 损坏证书纠错输出标签
     */
    public static final OutputTag<Row> CORRUPTED_CERT_CORRECTION_TAG =
            new OutputTag<Row>("corrupted-cert-correction") {};

    /**
     * 格式错误纠错输出标签
     */
    public static final OutputTag<Row> FORMAT_ERROR_CORRECTION_TAG =
            new OutputTag<Row>("format-error-correction") {};

    /**
     * 编码错误纠错输出标签
     */
    public static final OutputTag<Row> ENCODING_ERROR_CORRECTION_TAG =
            new OutputTag<Row>("encoding-error-correction") {};

    // 私有构造函数，防止实例化
    private CorrectionTags() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
}
