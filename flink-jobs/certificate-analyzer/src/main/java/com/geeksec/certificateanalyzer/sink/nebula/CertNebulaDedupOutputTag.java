package com.geeksec.certificateanalyzer.output.nebula;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * 证书Nebula去重输出标签
 * 用于证书Nebula图数据去重流程的流控制
 *
 * <AUTHOR>
 */
public final class CertNebulaDedupOutputTag {
    private CertNebulaDedupOutputTag() {
        // 防止实例化
    }

    /** 去重的Nebula证书数据标签 */
    public static final OutputTag<Row> DEDUP_NEBULA_CERT =
        new OutputTag<>("dedup-nebula-cert", TypeInformation.of(Row.class));

    /** 未去重的Nebula证书数据标签 */
    public static final OutputTag<Row> NOT_DEDUP_NEBULA_CERT =
        new OutputTag<>("not-dedup-nebula-cert", TypeInformation.of(Row.class));

    /** 未去重的Nebula颁发者数据标签 */
    public static final OutputTag<Row> NOT_DEDUP_NEBULA_ISSUER =
        new OutputTag<>("not-dedup-nebula-issuer", TypeInformation.of(Row.class));

    /** 未去重的Nebula主题数据标签 */
    public static final OutputTag<Row> NOT_DEDUP_NEBULA_SUBJECT =
        new OutputTag<>("not-dedup-nebula-subject", TypeInformation.of(Row.class));

    /** 未去重的Nebula URL数据标签 */
    public static final OutputTag<Row> NOT_DEDUP_NEBULA_URL =
        new OutputTag<>("not-dedup-nebula-url", TypeInformation.of(Row.class));

    /** 未去重的Nebula组织数据标签 */
    public static final OutputTag<Row> NOT_DEDUP_NEBULA_ORG =
        new OutputTag<>("not-dedup-nebula-org", TypeInformation.of(Row.class));
}