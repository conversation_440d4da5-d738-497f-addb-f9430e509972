package com.geeksec.certificateanalyzer.operator.enrichment;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.pipeline.analysis.CertificateClassifier;
import com.geeksec.certificateanalyzer.pipeline.analysis.CertificateOrganizationalInfoExtractor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

/**
 * 证书数据增强器
 * 作为一个 Flink MapFunction，负责编排和调用各种提取器和分类器，
 * 对证书数据进行地理、组织、分类等多维度的信息增强。
 *
 *
 * <AUTHOR>
 */
public class MetadataExtractor extends RichMapFunction<X509Certificate, X509Certificate> {

    private transient GeographicInfoExtractor geoExtractor;
    private transient CertificateOrganizationalInfoExtractor organizationExtractor;
    private transient CertificateClassifier classifier;
    private transient KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        // 初始化各种提取器
        geoExtractor = new GeographicInfoExtractor(knowledgeBaseClient);
        organizationExtractor = new CertificateOrganizationalInfoExtractor(knowledgeBaseClient);
        classifier = new CertificateClassifier();
    }

    @Override
    public void close() throws Exception {
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        // 1. 提取地理信息
        geoExtractor.extract(certificate);

        // 2. 提取组织信息
        organizationExtractor.extract(certificate);

        // 3. 对证书进行分类
        classifier.classify(certificate);

        return certificate;
    }
}
