package com.geeksec.certificateanalyzer.util;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import org.apache.flink.configuration.Configuration;

/**
 * 知识库工具类
 * 统一管理KnowledgeBaseClient的创建和配置
 * 
 * <AUTHOR>
 * @date 2024/12/22
 */
public final class KnowledgeBaseUtils {
    
    /**
     * 从Flink配置中创建KnowledgeBaseClient
     * 
     * @param parameters Flink配置参数
     * @return KnowledgeBaseClient实例
     */
    public static KnowledgeBaseClient createClient(Configuration parameters) {
        String knowledgeBaseUrl = parameters.getString(
            CertificateConstants.CONFIG_KNOWLEDGE_BASE_URL, 
            CertificateConstants.DEFAULT_KNOWLEDGE_BASE_URL
        );
        return new KnowledgeBaseClient(knowledgeBaseUrl);
    }
    
    /**
     * 使用默认配置创建KnowledgeBaseClient
     * 
     * @return KnowledgeBaseClient实例
     */
    public static KnowledgeBaseClient createDefaultClient() {
        return new KnowledgeBaseClient();
    }
    
    /**
     * 使用指定URL创建KnowledgeBaseClient
     * 
     * @param url 知识库服务URL
     * @return KnowledgeBaseClient实例
     */
    public static KnowledgeBaseClient createClient(String url) {
        return new KnowledgeBaseClient(url);
    }
    
    private KnowledgeBaseUtils() {
        throw new UnsupportedOperationException(CertificateConstants.ERROR_UTILITY_CLASS_INSTANTIATION);
    }
}
