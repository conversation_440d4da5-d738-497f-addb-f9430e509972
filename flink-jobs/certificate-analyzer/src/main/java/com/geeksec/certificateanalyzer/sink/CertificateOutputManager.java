package com.geeksec.certificateanalyzer.sink.certificate;

import org.apache.flink.streaming.api.datastream.DataStream;

import com.geeksec.certificateanalyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.sink.minio.MinioFileSinkFactory;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书输出管理器
 * 负责管理所有与证书相关的输出
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Slf4j
public class CertificateOutputManager {

    /**
     * 添加证书相关的输出
     *
     * @param certificateStream 证书数据流
     */
    public static void addCertificateOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加证书相关输出");

        // 证书存储输出
        certificateStream.addSink(MinioFileSinkFactory.createCertificateSink())
                .name("证书MinIO存储")
                .setParallelism(CertificateAnalyzerConfig.getAnalyzerParallelism());
        log.info("已添加证书MinIO存储输出");

        // 证书关系图输出（Nebula）
        if (CertificateAnalyzerConfig.isNebulaEnabled()) {
            certificateStream.addSink(new com.geeksec.certificateanalyzer.output.nebula.CertNebulaSinkFunction())
                    .name("证书关系图")
                    .setParallelism(CertificateAnalyzerConfig.getNebulaParallelism());
            log.info("已添加证书关系图输出");
        }

        // 证书缓存输出（Redis）
        if (CertificateAnalyzerConfig.isRedisEnabled()) {
            certificateStream.addSink(new com.geeksec.certificateanalyzer.output.redis.RedisCertificateSink())
                    .name("证书Redis缓存")
                    .setParallelism(CertificateAnalyzerConfig.getRedisParallelism());
            log.info("已添加证书Redis缓存输出");
        }

        log.info("证书相关输出添加完成");
    }
}
