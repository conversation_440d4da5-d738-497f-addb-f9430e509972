package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 检测Quakbot恶意软件使用的证书
 */
@Slf4j
public class QuakbotDetector extends BaseCertificateDetector {
    
    public QuakbotDetector() {
        super("Quakbot Detector");
    }
    
    protected boolean hasAllRequiredFields(Map<String, String> map, String... keys) {
        if (map == null) return false;
        for (String key : keys) {
            if (!map.containsKey(key)) {
                return false;
            }
        }
        return true;
    }
    
    protected String getStringValue(Map<String, String> map, String key) {
        if (map == null || key == null) return "";
        String value = map.get(key);
        return value != null ? value.toLowerCase() : "";
    }
    
    protected boolean isReadableText(String text, boolean allowNumbers) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 检查是否只包含可打印ASCII字符
        if (!text.matches("^[\\x20-\\x7E]+$")) {
            return false;
        }
        
        // 检查是否包含至少一个字母
        if (!text.matches(".*[a-zA-Z].*")) {
            return false;
        }
        
        // 如果不允许数字，检查是否包含数字
        if (!allowNumbers && text.matches(".*\\d.*")) {
            return false;
        }
        
        return true;
    }
    
    protected boolean isSelfSigned(X509Certificate cert) {
        if (cert == null) {
            return false;
        }
        
        Map<String, String> subject = cert.getSubject();
        Map<String, String> issuer = cert.getIssuer();
        
        if (subject == null || issuer == null) {
            return false;
        }
        
        // 比较主题和颁发者的所有字段是否相同
        return subject.equals(issuer);
    }

    @Override
    public void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        Map<String, String> subject = cert.getSubject();
        Map<String, String> issuer = cert.getIssuer();

        // 检查主题字段
        if (!hasAllRequiredFields(subject, "CN", "OU", "C") || subject.size() != 3) {
            return;
        }

        // 检查颁发者字段
        if (!hasAllRequiredFields(issuer, "CN", "L", "O", "ST", "C") || issuer.size() != 5) {
            return;
        }

        // 检查CN是否相同
        String subjectCN = getStringValue(subject, "CN");
        String issuerCN = getStringValue(issuer, "CN");
        
        if (!subjectCN.equals(issuerCN) || isSelfSigned(cert)) {
            return;
        }

        // 检查各个字段是否可读（与原始代码一致）
        if (isReadableText(subject.get("CN"), false) ||
            isReadableText(subject.get("OU"), false) ||
            isReadableText(issuer.get("CN"), false) ||
            isReadableText(issuer.get("L"), false) ||
            isReadableText(issuer.get("O"), false) ||
            isReadableText(issuer.get("ST"), false)) {
            // 检查序列号长度是否为4位16进制
            String serialNumber = cert.getSerialNumber();
            try {
                if (serialNumber != null && serialNumber.matches("[0-9a-fA-F]{4}")) {
                    cert.getLabels().add(CertificateLabel.BOTNET);
                    log.debug("Detected potential Quakbot certificate: {}", cert.getCommonName());
                }
            } catch (Exception e) {
                log.debug("Serial number check failed for Quakbot detection", e);
            }
        }
    }
}
