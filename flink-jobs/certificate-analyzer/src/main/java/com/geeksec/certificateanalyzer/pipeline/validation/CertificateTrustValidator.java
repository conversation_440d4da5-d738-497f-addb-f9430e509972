package com.geeksec.certificateanalyzer.pipeline.validation;

import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.CertPath;
import java.security.cert.CertPathValidator;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateFactory;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.PKIXParameters;
import java.util.Collections;

import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateTrustStatus;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书信任状态验证器
 * <p>
 * 负责根据系统配置的可信CA库，对证书进行信任状态评估。
 * 这是一个有状态的MapFunction，在open()方法中加载可信CA库，避免重复加载。
 *
 * <AUTHOR>
 * @since 2025/06/22
 */
@Slf4j
public class CertificateTrustValidator implements MapFunction<X509Certificate, X509Certificate> {

    private transient CertPathValidator certPathValidator;
    private transient PKIXParameters pkixParameters;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        try {
            // 1. 初始化证书路径验证器
            certPathValidator = CertPathValidator.getInstance("PKIX");

            // 2. 加载可信CA证书库
            KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
            // 必须先调用load(null)，否则无法添加证书
            trustStore.load(null, null);

            try (InputStream is = getClass().getClassLoader().getResourceAsStream("trust_store.pem")) {
                if (is == null) {
                    log.error("关键错误：无法在资源中找到可信CA库 'trust_store.pem'。所有证书将被视为不可信。");
                    // 创建一个空的信任库，这样作业不会失败，但所有验证都会失败
                } else {
                    CertificateFactory cf = CertificateFactory.getInstance("X.509");
                    cf.generateCertificates(is).forEach(cert -> {
                        try {
                            trustStore.setCertificateEntry(((java.security.cert.X509Certificate) cert).getSubjectX500Principal().getName(), cert);
                        } catch (Exception e) {
                            log.warn("加载可信CA证书时出错", e);
                        }
                    });
                }
            }

            // 3. 配置PKIX参数，用于证书路径验证
            pkixParameters = new PKIXParameters(trustStore);
            // 默认不检查吊销状态，可以根据需要开启
            pkixParameters.setRevocationEnabled(false);

            log.info("证书信任验证器初始化完成，共加载 {} 个可信CA证书。", trustStore.size());

        } catch (Exception e) {
            log.error("证书信任验证器初始化失败！", e);
            throw new RuntimeException("无法初始化证书信任验证器", e);
        }
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        // 如果验证器未成功初始化，则直接返回未知状态
        if (certPathValidator == null || pkixParameters == null) {
            certificate.setTrustStatus(CertificateTrustStatus.UNKNOWN);
            return certificate;
        }

        try {
            // 将待验证的证书转换为Java的X509Certificate对象
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            java.security.cert.X509Certificate certToValidate = (java.security.cert.X509Certificate) cf.generateCertificate(
                    new java.io.ByteArrayInputStream(certificate.getCert()));

            // 检查是否自签名
            if (isSelfSigned(certToValidate)) {
                certificate.setTrustStatus(CertificateTrustStatus.SELF_SIGNED);
                return certificate;
            }

            // 检查是否过期
            try {
                certToValidate.checkValidity();
            } catch (CertificateExpiredException | CertificateNotYetValidException e) {
                certificate.setTrustStatus(CertificateTrustStatus.EXPIRED);
                return certificate;
            }

            // 构建证书路径并进行验证
            CertPath certPath = cf.generateCertPath(Collections.singletonList(certToValidate));
            certPathValidator.validate(certPath, pkixParameters);

            // 如果验证成功，则认为是可信的
            certificate.setTrustStatus(CertificateTrustStatus.TRUSTED);

        } catch (CertPathValidatorException e) {
            // 验证失败，说明无法构建到可信根的信任链
            certificate.setTrustStatus(CertificateTrustStatus.UNTRUSTED);
            log.debug("证书验证失败 ({}): {}", certificate.getCommonName(), e.getMessage());
        } catch (Exception e) {
            // 其他异常，标记为未知
            certificate.setTrustStatus(CertificateTrustStatus.UNKNOWN);
            log.warn("证书验证过程中发生未知异常 for {}", certificate.getCommonName(), e);
        }

        return certificate;
    }

    private boolean isSelfSigned(java.security.cert.X509Certificate cert) {
        try {
            // 如果颁发者和主体相同，并且能用自己的公钥验证签名，则为自签名
            if (cert.getIssuerX500Principal().equals(cert.getSubjectX500Principal())) {
                cert.verify(cert.getPublicKey());
                return true;
            }
        } catch (Exception e) {
            // 验证失败，不是有效的自签名证书
            return false;
        }
        return false;
    }
}
