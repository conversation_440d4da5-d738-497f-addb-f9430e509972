package com.geeksec.certificateanalyzer.operator.preprocessing;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateSource;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.db.postgresql.CertificatePostgreSQLRepository;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/8/9
 */
@Slf4j
public class UserInfoMapFunction extends RichMapFunction<X509Certificate, X509Certificate> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        getTaskUserInfo(cert);
        return cert;
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    // 获取证书的TaskID 和用户名信息
    public static void getTaskUserInfo(X509Certificate cert) throws SQLException {
        CertificateSource source = cert.getSource();
        // 判断证书是否是由任务导入，是的话增加task_id
        if (source != null && source.isUserCertificate()) {
            Set<String> taskId = getTaskId(cert);
            Set<String> batchId = getBatchId(cert);
            String[] taskIdList = taskId.toArray(new String[taskId.size()]);
            String[] batchIdList = batchId.toArray(new String[batchId.size()]);
            if (taskIdList.length > 0) {
                // TODO 此处taskid是否应该设置为和用户ID一样的去重列表形式
                cert.setTaskId(taskIdList[0]);
                // 获取用户ID列表,从PostgreSQL取，task在redis里去重了，用户ID在PostgreSQL去重了
                List<String> userIdList = CertificatePostgreSQLRepository.getPostgreSQLUserIDList(taskIdList);
                cert.setUserIDList(userIdList);
            } else {
                cert.setTaskId("0");
                cert.setUserIDList(Arrays.asList("unk"));
                log.error("cert_user中出现无来源信息证书，证书SHA1——{}——", cert.getASN1SHA1());
            }

            if (batchIdList.length > 0) {
                // TODO 此处 batch_id 是否应该设置为和用户ID一样的去重列表形式
                cert.setBatchId(batchIdList[0]);
            } else {
                cert.setBatchId("0");
                log.error("cert_user中出现无批次信息证书，证书SHA1——{}——", cert.getASN1SHA1());
            }
        } else {
            log.error("出现了已知外的证书来源");
            cert.setTaskId("0");
            cert.setBatchId("0");
            cert.setUserIDList(Arrays.asList("unkSourceName"));
        }
    }

    public static Set<String> getTaskId(X509Certificate cert) {
        String sha1 = cert.getASN1SHA1();
        Jedis jedis = null;
        Set<String> taskId = new HashSet<>();
        try {
            jedis = RedisUtils.getJedis(jedisPool);
            taskId = RedisUtils.getRedisTaskID(sha1, jedis, "taskId");
        } catch (Exception e) {
            log.error("证书SHA1为：{}的taskId查询失败，error--->{}", sha1, e.getCause());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return taskId;
    }

    public static Set<String> getBatchId(X509Certificate cert) {
        String sha1 = cert.getASN1SHA1();
        Jedis jedis = null;
        Set<String> batchId = new HashSet<>();
        try {
            jedis = RedisUtils.getJedis(jedisPool);
            batchId = RedisUtils.getRedisTaskID(sha1, jedis, "batchId");
        } catch (Exception e) {
            log.error("证书SHA1为：{}的taskId查询失败，error--->{}", sha1, e.getCause());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return batchId;
    }
}
