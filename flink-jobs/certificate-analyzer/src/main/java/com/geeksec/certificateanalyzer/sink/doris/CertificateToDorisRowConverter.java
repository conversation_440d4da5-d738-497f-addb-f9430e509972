package com.geeksec.certificateanalyzer.output.doris;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

import org.apache.flink.types.Row;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.enums.CertificateTrustStatus;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * X509证书到Flink Row的转换器
 * 负责将X509Certificate对象转换为Flink Row对象，便于后续写入Doris等存储
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateToDorisRowConverter implements DorisConverter<X509Certificate> {

    /**
     * 将X509Certificate转换为Flink Row
     *
     * @param cert 证书对象
     * @return Flink Row对象
     */
    public static Row convertToRow(X509Certificate cert) {
        try {
            Row row = Row.withNames();

            // 主键和基础哈希字段
            row.setField("der_sha1", cert.getDerSha1());
            row.setField("der_md5", cert.getDerMd5());
            row.setField("der_sha256", cert.getDerSha256());
            row.setField("pem_md5", cert.getPemMd5());
            row.setField("pem_sha256", cert.getPemSha256());
            row.setField("pem_sha1", cert.getPemSha1());

            // 证书基本信息
            row.setField("version", cert.getVersion());
            row.setField("serial_number", cert.getSerialNumber());
            row.setField("format", cert.getFormat());

            // 关联ID
            row.setField("issuer_id", cert.getIssuerId());
            row.setField("subject_id", cert.getSubjectId());

            // 时间相关
            row.setField("not_before", cert.getNotBefore() != null ? Timestamp.valueOf(cert.getNotBefore()) : null);
            row.setField("not_after", cert.getNotAfter() != null ? Timestamp.valueOf(cert.getNotAfter()) : null);
            row.setField("duration", cert.getDuration());
            row.setField("import_time", cert.getImportTime() != null ? Timestamp.valueOf(cert.getImportTime()) : null);

            // 名称和标识
            row.setField("common_name", extractCommonName(cert.getCommonName()));
            row.setField("subject_alt_names", convertToStringArray(cert.getSubjectAltNames()));
            row.setField("issuer_alt_names", convertToStringArray(cert.getIssuerAltNames()));

            // 公钥信息
            row.setField("public_key", cert.getPublicKey());
            row.setField("public_key_algorithm", cert.getPublicKeyAlgorithm());
            row.setField("public_key_length", cert.getPublicKeyLength());
            row.setField("public_key_parameter", cert.getPublicKeyParameter());
            row.setField("spki_sha256", cert.getSpkiSha256());
            row.setField("public_key_alg_oid", cert.getPublicKeyAlgOid());
            row.setField("public_key_param_oid", cert.getPublicKeyParamOid());

            // 签名信息
            row.setField("signature_algorithm", cert.getSignatureAlgorithm());
            row.setField("signature_alg_name", cert.getSignatureAlgName());
            row.setField("signature_alg_oid", cert.getSignatureAlgOid());

            // 密钥用途和扩展
            row.setField("key_usage", cert.getKeyUsage() != null ? cert.getKeyUsage() :
                    (cert.getCertificateExtensions() != null ? cert.getCertificateExtensions().getKeyUsage() : null));
            row.setField("extended_key_usage", !cert.getExtendedKeyUsage().isEmpty() ? convertToStringArray(cert.getExtendedKeyUsage()) :
                    (cert.getCertificateExtensions() != null
                            ? convertToStringArray(cert.getCertificateExtensions().getExtendedKeyUsage())
                            : null));
            row.setField("basic_constraints", cert.getBasicConstraints() != null ? cert.getBasicConstraints() :
                    (cert.getCertificateExtensions() != null ? cert.getCertificateExtensions().getBasicConstraints()
                            : null));
            row.setField("authority_key_identifier", cert.getAuthorityKeyIdentifier() != null ? cert.getAuthorityKeyIdentifier() :
                    (cert.getCertificateExtensions() != null
                            ? cert.getCertificateExtensions().getAuthorityKeyIdentifier()
                            : null));
            row.setField("subject_key_identifier", cert.getSubjectKeyIdentifier() != null ? cert.getSubjectKeyIdentifier() :
                    (cert.getCertificateExtensions() != null ? cert.getCertificateExtensions().getSubjectKeyIdentifier()
                            : null));
            row.setField("crl_distribution_points", !cert.getCrlDistributionPoints().isEmpty() ? convertToStringArray(cert.getCrlDistributionPoints()) :
                    (cert.getCertificateExtensions() != null
                            ? convertToStringArray(cert.getCertificateExtensions().getCrlDistributionPoints())
                            : null));
            row.setField("authority_info_access", !cert.getAuthorityInfoAccess().isEmpty() ? convertToStringArray(cert.getAuthorityInfoAccess()) :
                    (cert.getCertificateExtensions() != null
                            ? convertToStringArray(cert.getCertificateExtensions().getAuthorityInfoAccess())
                            : null));
            row.setField("subject_info_access", !cert.getSubjectInfoAccess().isEmpty() ? convertToStringArray(cert.getSubjectInfoAccess()) :
                    (cert.getCertificateExtensions() != null
                            ? convertToStringArray(cert.getCertificateExtensions().getSubjectInfoAccess())
                            : null));
            row.setField("cert_policies", !cert.getCertPolicies().isEmpty() ? convertToStringArray(cert.getCertPolicies()) :
                    (cert.getCertificateExtensions() != null
                            ? convertToStringArray(cert.getCertificateExtensions().getCertPolicies())
                            : null));

            // 扩展信息
            row.setField("extensions", cert.getCertificateExtensions() != null ? cert.getCertificateExtensions().getExtensionMap() : null);

            // 证书链信息
            row.setField("parent_cert_id", cert.getParentCertId());
            row.setField("cert_chain_ids", convertToStringArray(cert.getCertChainIds()));

            // 业务分类字段
            row.setField("source", cert.getSource());
            row.setField("user_type", cert.getUserType());
            row.setField("business_type", cert.getBusinessType());
            row.setField("ca_type", cert.getCaType());
            row.setField("industry_type", cert.getIndustryType());
            row.setField("subject_area", cert.getSubjectArea());
            row.setField("issuer_area", cert.getIssuerArea());

            // 状态和标记
            row.setField("is_whitelisted", cert.getTrustStatus() == CertificateTrustStatus.TRUSTED ? 1 : 0);
            row.setField("is_blacklisted", (cert.getThreatScore() > 80 ||
                    (cert.getLabels() != null && cert.getLabels().stream()
                            .anyMatch(label -> label.toString().contains("Threat") ||
                                             label.toString().contains("Malicious") ||
                                             label.toString().contains("APT")))) ? 1 : 0);
            row.setField("is_parsed_successfully", cert.getIsParsedSuccessfully() != null ? (cert.getIsParsedSuccessfully() ? 1 : 0) : 0);
            row.setField("is_corrupted", cert.getIsCorrupted() != null ? (cert.getIsCorrupted() ? 1 : 0) : 0);
            row.setField("cert_occurrence_count", cert.getCertOccurrenceCount());
            row.setField("processing_method", cert.getProcessingMethod() != null ? cert.getProcessingMethod().toString() : null);
            row.setField("parse_status", cert.getIsParsedSuccessfully() != null && cert.getIsParsedSuccessfully() ? "SUCCESS" : "FAILED");

            // 任务和批次信息
            row.setField("task_id", cert.getTaskId());
            row.setField("batch_id", cert.getBatchId());
            row.setField("user_id_list", convertToStringArray(cert.getUserIdList()));

            // 评分字段
            row.setField("threat_score", cert.getThreatScore());
            row.setField("trust_score", cert.getTrustScore());
            row.setField("threat_level", cert.getThreatLevel());

            // 标签和关联信息
            row.setField("labels", convertLabelsToIntArray(cert.getLabels()));
            row.setField("forward_chunk_hashes", convertToStringArray(cert.getForwardChunkHashes()));
            row.setField("reverse_chunk_hashes", convertToStringArray(cert.getReverseChunkHashes()));
            row.setField("corrected_asn1_sha1", cert.getCorrectedAsn1Sha1());

            // 证书内嵌信息
            row.setField("cert_domains", convertToStringArray(cert.getCertDomains()));
            row.setField("cert_ips", convertToStringArray(cert.getCertIps()));

            // 特殊字段
            row.setField("uncommon_oids", convertToStringArray(cert.getUncommonOids()));
            row.setField("organization", cert.getOrganization());

            // 时间字段
            row.setField("first_seen", cert.getFirstSeen() != null ? Timestamp.valueOf(cert.getFirstSeen()) : null);
            row.setField("last_seen", cert.getLastSeen() != null ? Timestamp.valueOf(cert.getLastSeen()) : null);
            row.setField("create_time", cert.getCreateTime() != null ? Timestamp.valueOf(cert.getCreateTime()) : null);
            row.setField("update_time", cert.getUpdateTime() != null ? Timestamp.valueOf(cert.getUpdateTime()) : null);
            row.setField("remark", cert.getRemark());

            return row;
        } catch (Exception e) {
            log.error("转换证书数据到Row失败: {}", cert.getDerSha1(), e);
            throw new RuntimeException("证书数据转换失败", e);
        }
    }

    /**
     * 提取通用名称
     */
    private static String extractCommonName(Object cn) {
        if (cn == null) {
            return null;
        }
        if (cn instanceof String) {
            return (String) cn;
        }
        if (cn instanceof List) {
            List<?> cnList = (List<?>) cn;
            return cnList.isEmpty() ? null : cnList.get(0).toString();
        }
        return cn.toString();
    }

    /**
     * 转换为字符串数组
     */
    private static String[] convertToStringArray(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            return list.stream().map(Object::toString).toArray(String[]::new);
        }
        if (obj instanceof String[]) {
            return (String[]) obj;
        }
        if (obj instanceof String) {
            return new String[] { (String) obj };
        }
        return new String[] { obj.toString() };
    }

    /**
     * 转换标签为整数数组
     * 将CertificateLabel枚举转换为对应的整数值
     */
    private static Integer[] convertLabelsToIntArray(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Set) {
            Set<?> set = (Set<?>) obj;
            return set.stream()
                    .filter(item -> item instanceof CertificateLabel)
                    .map(item -> ((CertificateLabel) item).ordinal())
                    .toArray(Integer[]::new);
        }
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            return list.stream()
                    .filter(item -> item instanceof CertificateLabel)
                    .map(item -> ((CertificateLabel) item).ordinal())
                    .toArray(Integer[]::new);
        }
        return null;
    }





}
