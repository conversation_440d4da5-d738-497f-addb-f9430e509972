package com.geeksec.certificateanalyzer.pipeline.analysis.signature.result;

import lombok.Builder;
import lombok.Getter;
import org.apache.flink.types.Row;

/**
 * 证书验证结果
 * 封装验证的结果信息
 * 
 * <AUTHOR>
 */
@Getter
@Builder
public class ValidationResult {
    
    @Builder.Default
    private final boolean shouldContinue = false;
    @Builder.Default
    private final boolean isValid = true;
    @Builder.Default
    private final String message = "";
    private final Row outputRow;
    @Builder.Default
    private final ValidationAction action = ValidationAction.STOP_VALIDATION;
    

    
    /**
     * 验证动作枚举
     */
    public enum ValidationAction {
        CONTINUE_VALIDATION("继续验证"),
        STOP_VALIDATION("停止验证"),
        GOTO_EVALUATION("转到评估");
        
        private final String description;
        
        ValidationAction(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    

    
    // 便捷方法
    public static ValidationResult continueValidation(Row outputRow) {
        return builder()
                .shouldContinue(true)
                .isValid(true)
                .action(ValidationAction.CONTINUE_VALIDATION)
                .outputRow(outputRow)
                .build();
    }
    
    public static ValidationResult stopValidation(Row outputRow, String message) {
        return builder()
                .shouldContinue(false)
                .action(ValidationAction.STOP_VALIDATION)
                .outputRow(outputRow)
                .message(message)
                .build();
    }
    
    public static ValidationResult gotoEvaluation(Row outputRow) {
        return builder()
                .shouldContinue(false)
                .action(ValidationAction.GOTO_EVALUATION)
                .outputRow(outputRow)
                .build();
    }
    

}
