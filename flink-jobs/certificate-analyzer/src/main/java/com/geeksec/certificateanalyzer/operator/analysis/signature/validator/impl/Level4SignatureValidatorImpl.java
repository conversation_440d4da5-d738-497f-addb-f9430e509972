package com.geeksec.certificateanalyzer.pipeline.analysis.signature.validator.impl;

import com.geeksec.certificateanalyzer.pipeline.analysis.signature.context.CertificateValidationContext;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.result.ValidationResult;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.service.CertificateChainValidationService;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.validator.ChainCertificateValidator;
import com.geeksec.certificateanalyzer.pipeline.common.outputtags.ValidationOutputTags;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

/**
 * 四级证书签名验证器实现
 * 负责执行第四级证书链签名验证，处理极深层次的信任关系
 * 
 * <AUTHOR>
 */
@Slf4j
public class Level4SignatureValidatorImpl extends AbstractCertificateValidatorImpl 
        implements ChainCertificateValidator {
    
    private final CertificateChainValidationService chainValidationService;
    
    public Level4SignatureValidatorImpl(CertificateChainValidationService chainValidationService) {
        super(ValidationLevel.LEVEL_4);
        this.chainValidationService = chainValidationService;
    }
    
    @Override
    public ValidationResult validateChain(CertificateValidationContext context) {
        try {
            log.debug("四级签名验证处理证书: {}", context.getCertificate().getDerSha1());
            
            // 创建输出行数据
            Row outputRow = createOutputRow(context.getCertificate(), context.getParentCertificateMap());
            
            // 使用证书链验证服务进行验证
            boolean shouldContinueValidation = chainValidationService.validateCertificateChain(
                    context.getCertificate(),
                    context.getParentCertificateIds(),
                    context.getSecurityLabels());
            
            // 更新输出行中的证书信息
            context.getCertificate().setLabels(context.getSecurityLabels());
            context.getCertificate().setParentCertIdList(context.getParentCertificateIds());
            outputRow.setField(0, context.getCertificate());
            
            if (shouldContinueValidation) {
                log.debug("四级签名验证需要继续验证: {}", context.getCertificate().getDerSha1());
                return ValidationResult.continueValidation(outputRow);
            } else {
                log.debug("四级签名验证完成: {}", context.getCertificate().getDerSha1());
                return ValidationResult.stopValidation(outputRow, "四级验证完成");
            }
            
        } catch (Exception e) {
            log.error("四级签名验证处理证书时发生异常", e);
            Row errorRow = createOutputRow(context.getCertificate(), context.getParentCertificateMap());
            return ValidationResult.stopValidation(errorRow, "验证异常");
        }
    }
    
    @Override
    protected void handleValidationResult(ValidationResult result, Context ctx) {
        try {
            switch (result.getAction()) {
                case CONTINUE_VALIDATION:
                    ctx.output(ValidationOutputTags.CONTINUE_VALIDATION_4, result.getOutputRow());
                    break;
                case STOP_VALIDATION:
                    ctx.output(ValidationOutputTags.STOP_VALIDATION_4, result.getOutputRow());
                    break;
                case GOTO_EVALUATION:
                    ctx.output(ValidationOutputTags.GOTO_EVALUATION, result.getOutputRow());
                    break;
                default:
                    log.warn("未知的验证动作: {}", result.getAction());
                    ctx.output(ValidationOutputTags.STOP_VALIDATION_4, result.getOutputRow());
                    break;
            }
        } catch (Exception e) {
            log.error("处理验证结果时发生异常", e);
        }
    }
}
