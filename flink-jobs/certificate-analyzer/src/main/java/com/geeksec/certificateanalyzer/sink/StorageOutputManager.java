package com.geeksec.certificateanalyzer.sink.storage;

import com.geeksec.certificateanalyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 存储输出管理器
 * 负责管理所有与存储相关的输出
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Slf4j
public class StorageOutputManager {

    /**
     * 添加存储相关的输出
     *
     * @param certificateStream 证书数据流
     */
    public static void addStorageOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加存储相关输出");



        // 对象存储输出（MinIO - 必须启用）
        // 原始证书文件存储
        certificateStream
                .filter(cert -> cert.getCert() != null && cert.getCert().length > 0)
                .addSink(com.geeksec.certificateanalyzer.output.minio.MinioSinkFactory.createCertificateSink())
                .name("证书文件MinIO存储")
                .setParallelism(CertificateAnalyzerConfig.getAnalyzerParallelism());
        log.info("已添加证书文件MinIO存储输出");

        // 备份存储输出
        addBackupStorageOutputs(certificateStream);

        log.info("存储相关输出添加完成");
    }

    /**
     * 添加备份存储输出
     *
     * @param certificateStream 证书数据流
     */
    private static void addBackupStorageOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加备份存储输出");

        // 重要证书备份（包含特殊标签的证书）
        DataStream<X509Certificate> importantCertStream = certificateStream
                .filter(cert -> cert.getLabels().stream()
                        .anyMatch(tag -> tag.contains("Root CA") || tag.contains("White CA") || 
                                       tag.contains("EV Cert") || tag.contains("Threat")))
                .name("重要证书过滤");

        // 重要证书MinIO备份（必须启用）
        importantCertStream.addSink(com.geeksec.certificateanalyzer.output.minio.MinioSinkFactory.createCertificateSink())
                .name("重要证书MinIO备份")
                .setParallelism(2);

        log.info("已添加重要证书MinIO备份输出");

        // TODO: 如果需要Redis备份，可以在这里添加

        log.info("备份存储输出添加完成");
    }



    /**
     * 重要证书Redis备份Sink
     */
    private static class ImportantCertRedisBackupSink extends com.geeksec.certificateanalyzer.output.redis.RedisCertificateSink {
        // 可以在这里自定义重要证书的特殊缓存逻辑
        // 比如使用不同的Redis数据库，或者设置更长的过期时间
    }
}
