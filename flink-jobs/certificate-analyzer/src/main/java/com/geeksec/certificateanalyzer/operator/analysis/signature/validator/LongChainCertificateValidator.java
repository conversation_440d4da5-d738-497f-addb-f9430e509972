package com.geeksec.certificateanalyzer.pipeline.analysis.signature.validator;

import com.geeksec.certificateanalyzer.operator.analysis.signature.context.CertificateValidationContext;
import com.geeksec.certificateanalyzer.operator.analysis.signature.result.ValidationResult;

/**
 * 长链证书验证器接口
 * 专门用于处理长证书链的验证器
 * 
 * <AUTHOR>
 */
public interface LongChainCertificateValidator extends CertificateValidator {
    
    /**
     * 验证长证书链
     * 
     * @param context 验证上下文
     * @return 验证结果
     */
    ValidationResult validateLongChain(CertificateValidationContext context);
    
    /**
     * 检查证书链是否过长
     * 
     * @param parentCertificateIds 父证书ID列表
     * @param threshold 阈值
     * @return 是否过长
     */
    boolean isChainTooLong(java.util.List<String> parentCertificateIds, int threshold);
    
    /**
     * 默认实现，调用长链验证
     */
    @Override
    default ValidationResult validate(CertificateValidationContext context) {
        return validate<PERSON><PERSON><PERSON><PERSON><PERSON>(context);
    }
}
