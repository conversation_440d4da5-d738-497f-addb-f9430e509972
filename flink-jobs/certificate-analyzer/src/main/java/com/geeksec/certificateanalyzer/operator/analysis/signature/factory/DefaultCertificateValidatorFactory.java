package com.geeksec.certificateanalyzer.pipeline.analysis.signature.factory;

import com.geeksec.certificateanalyzer.operator.analysis.signature.service.CertificateChainValidationService;
import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.CertificateValidator;
import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认证书验证器工厂实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultCertificateValidatorFactory implements CertificateValidatorFactory {
    
    private final CertificateChainValidationService chainValidationService;

    public DefaultCertificateValidatorFactory(
            CertificateChainValidationService chainValidationService) {
        this.chainValidationService = chainValidationService;
    }
    
    @Override
    public CertificateValidator createValidator(CertificateValidator.ValidationLevel level) {
        switch (level) {
            case LEVEL_1:
                return new Level1SignatureValidatorImpl(chainValidationService);
            case LEVEL_2:
                return new Level2SignatureValidatorImpl(chainValidationService);
            case LEVEL_3:
                return new Level3SignatureValidatorImpl(chainValidationService);
            case LEVEL_4:
                return new Level4SignatureValidatorImpl(chainValidationService);
            case LEVEL_5:
                return new LongChainSignatureValidatorImpl();
            default:
                throw new IllegalArgumentException("不支持的验证级别: " + level);
        }
    }
    
    @Override
    public CertificateValidator.ValidationLevel[] getSupportedLevels() {
        return CertificateValidator.ValidationLevel.values();
    }
}
