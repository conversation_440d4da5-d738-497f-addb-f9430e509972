package com.geeksec.certificateanalyzer.pipeline.analysis.signature.factory;

import com.geeksec.certificateanalyzer.operator.analysis.signature.registry.ServiceRegistry;
import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.CertificateValidator;
import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;

/**
 * 证书签名验证器工厂
 * 为Flink流处理提供ProcessFunction形式的证书签名验证器实例
 * 
 * <AUTHOR>
 */
@Slf4j
public class CertificateSignatureValidatorFactory {
    
    private static final ServiceRegistry serviceRegistry = ServiceRegistry.getInstance();
    
    static {
        // 确保服务注册器已初始化
        serviceRegistry.initialize();
    }
    
    /**
     * 创建一级签名验证器
     */
    public static ProcessFunction<Row, Row> createLevel1Validator() {
        try {
            CertificateValidatorFactory factory = serviceRegistry.getService(CertificateValidatorFactory.class);
            CertificateValidator validator = factory.createValidator(CertificateValidator.ValidationLevel.LEVEL_1);
            
            if (validator instanceof Level1SignatureValidatorImpl) {
                return (Level1SignatureValidatorImpl) validator;
            } else {
                throw new IllegalStateException("期望Level1SignatureValidatorImpl，但得到: " + validator.getClass());
            }
        } catch (Exception e) {
            log.error("创建一级签名验证器失败", e);
            throw new RuntimeException("创建一级签名验证器失败", e);
        }
    }
    
    /**
     * 创建二级签名验证器
     */
    public static ProcessFunction<Row, Row> createLevel2Validator() {
        try {
            CertificateValidatorFactory factory = serviceRegistry.getService(CertificateValidatorFactory.class);
            CertificateValidator validator = factory.createValidator(CertificateValidator.ValidationLevel.LEVEL_2);
            
            if (validator instanceof Level2SignatureValidatorImpl) {
                return (Level2SignatureValidatorImpl) validator;
            } else {
                throw new IllegalStateException("期望Level2SignatureValidatorImpl，但得到: " + validator.getClass());
            }
        } catch (Exception e) {
            log.error("创建二级签名验证器失败", e);
            throw new RuntimeException("创建二级签名验证器失败", e);
        }
    }
    
    /**
     * 创建三级签名验证器
     */
    public static ProcessFunction<Row, Row> createLevel3Validator() {
        try {
            CertificateValidatorFactory factory = serviceRegistry.getService(CertificateValidatorFactory.class);
            CertificateValidator validator = factory.createValidator(CertificateValidator.ValidationLevel.LEVEL_3);
            
            if (validator instanceof Level3SignatureValidatorImpl) {
                return (Level3SignatureValidatorImpl) validator;
            } else {
                throw new IllegalStateException("期望Level3SignatureValidatorImpl，但得到: " + validator.getClass());
            }
        } catch (Exception e) {
            log.error("创建三级签名验证器失败", e);
            throw new RuntimeException("创建三级签名验证器失败", e);
        }
    }
    
    /**
     * 创建四级签名验证器
     */
    public static ProcessFunction<Row, Row> createLevel4Validator() {
        try {
            CertificateValidatorFactory factory = serviceRegistry.getService(CertificateValidatorFactory.class);
            CertificateValidator validator = factory.createValidator(CertificateValidator.ValidationLevel.LEVEL_4);
            
            if (validator instanceof Level4SignatureValidatorImpl) {
                return (Level4SignatureValidatorImpl) validator;
            } else {
                throw new IllegalStateException("期望Level4SignatureValidatorImpl，但得到: " + validator.getClass());
            }
        } catch (Exception e) {
            log.error("创建四级签名验证器失败", e);
            throw new RuntimeException("创建四级签名验证器失败", e);
        }
    }
    
    /**
     * 创建长链签名验证器
     */
    public static ProcessFunction<Row, Row> createLongChainValidator() {
        try {
            CertificateValidatorFactory factory = serviceRegistry.getService(CertificateValidatorFactory.class);
            CertificateValidator validator = factory.createValidator(CertificateValidator.ValidationLevel.LEVEL_5);
            
            if (validator instanceof LongChainSignatureValidatorImpl) {
                return (LongChainSignatureValidatorImpl) validator;
            } else {
                throw new IllegalStateException("期望LongChainSignatureValidatorImpl，但得到: " + validator.getClass());
            }
        } catch (Exception e) {
            log.error("创建长链签名验证器失败", e);
            throw new RuntimeException("创建长链签名验证器失败", e);
        }
    }
}
