package com.geeksec.certificateanalyzer.sink.alarm;

import org.apache.flink.types.Row;

import lombok.extern.slf4j.Slf4j;

/**
 * 指纹告警构建器
 * 专门负责构建指纹相关的告警数据
 * 
 * <AUTHOR>
 */
@Slf4j
public final class FingerAlarmBuilder {
    
    /**
     * 私有构造函数，防止实例化
     */
    private FingerAlarmBuilder() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 构建指纹告警行记录
     * 
     * @param alarmTypeText 告警类型文本
     * @param fingerTypeText 指纹类型文本
     * @param fingerTagRow 指纹标签行
     * @param fingerLabel 指纹标签
     * @return 告警行记录
     */
    public static Row buildFingerAlarmRow(String alarmTypeText, String fingerTypeText, 
                                         Row fingerTagRow, String fingerLabel) {
        Row alarmRow = new Row(7);
        String ja3 = fingerTagRow.getFieldAs(2).toString();
        
        alarmRow.setField(0, alarmTypeText);
        alarmRow.setField(1, fingerTypeText);
        alarmRow.setField(2, fingerTagRow.getField(5)); // sip
        alarmRow.setField(3, fingerTagRow.getField(6)); // dip
        alarmRow.setField(4, fingerTagRow.getField(1).toString()); // fingerID
        alarmRow.setField(5, fingerLabel);
        alarmRow.setField(6, fingerTagRow.getField(8)); // 会话ID
        
        log.info("指纹检测告警，告警类型：{}，指纹ja3：{}", alarmTypeText, ja3);
        return alarmRow;
    }
}
