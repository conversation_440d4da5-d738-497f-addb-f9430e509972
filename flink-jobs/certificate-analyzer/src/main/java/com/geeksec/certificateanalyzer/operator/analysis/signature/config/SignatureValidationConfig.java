package com.geeksec.certificateanalyzer.pipeline.analysis.signature.config;

import com.geeksec.certificateanalyzer.pipeline.analysis.signature.factory.CertificateValidatorFactory;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.factory.DefaultCertificateValidatorFactory;
import com.geeksec.certificateanalyzer.pipeline.analysis.signature.service.*;
import com.geeksec.certificateanalyzer.repository.CertificateRepository;
import com.geeksec.certificateanalyzer.repository.doris.DorisCertificateRepository;
import com.geeksec.certificateanalyzer.repository.doris.DorisConnectionProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 签名验证配置类
 * 配置所有签名验证相关的Bean
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SignatureValidationConfig {
    
    /**
     * 配置证书仓库
     */
    @Bean
    @ConditionalOnMissingBean(CertificateRepository.class)
    public CertificateRepository certificateRepository() {
        log.info("创建默认证书仓库: DorisCertificateRepository");
        DorisConnectionProvider connectionProvider = new DorisConnectionProvider();
        return new DorisCertificateRepository(connectionProvider);
    }
    
    /**
     * 配置可信证书服务
     */
    @Bean
    public TrustedCertificateService trustedCertificateService(CertificateRepository certificateRepository) {
        log.info("创建可信证书服务");
        return new TrustedCertificateService(certificateRepository);
    }
    
    /**
     * 配置签名验证服务
     */
    @Bean
    public SignatureVerificationService signatureVerificationService(CertificateRepository certificateRepository) {
        log.info("创建签名验证服务");
        return new SignatureVerificationService(certificateRepository);
    }
    

    
    /**
     * 配置证书链验证服务
     */
    @Bean
    public CertificateChainValidationService certificateChainValidationService(
            TrustedCertificateService trustedCertificateService,
            SignatureVerificationService signatureVerificationService) {
        log.info("创建证书链验证服务");
        return new CertificateChainValidationService(
                trustedCertificateService,
                signatureVerificationService);
    }
    
    /**
     * 配置证书验证器工厂
     */
    @Bean
    public CertificateValidatorFactory certificateValidatorFactory(
            CertificateChainValidationService chainValidationService) {
        log.info("创建证书验证器工厂");
        return new DefaultCertificateValidatorFactory(chainValidationService);
    }
}
