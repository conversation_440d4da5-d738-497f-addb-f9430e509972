package com.geeksec.certificateanalyzer.operator.analysis;

import com.geeksec.common.infrastructure.network.NetworkUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.cert.CertificateNameParser;
import com.geeksec.certificateanalyzer.util.validation.TorAddressValidator;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

/**
 * 证书威胁分析器
 * 负责检测证书中的威胁特征，包括APT、Botnet、Tor、C&C等威胁相关标签
 * 
 * <AUTHOR>
 * @date 2024/12/22
 */
@Slf4j
public class ThreatPatternAnalyzer extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 知识库客户端 */
    private KnowledgeBaseClient knowledgeBaseClient;
    
    /** C&C威胁域名列表 */
    private Set<String> c2ThreatDomainList = new HashSet<>();
    
    /** C&C威胁IP列表 */
    private Set<String> c2ThreatIpList = new HashSet<>();
    
    /** 恶意域名列表 */
    private Set<String> maliciousDomainList = new HashSet<>();
    
    /** IOC IP列表 */
    private Set<String> iocIpList = new HashSet<>();
    
    /** 挖矿域名列表 */
    private Set<String> mineDomainList = new HashSet<>();
    
    /** Tranco Top域名列表 */
    private Set<String> trancoTopDomainList = new HashSet<>();

    /** Tranco Top域名映射 */
    private Map<String, Integer> trancoTopDomainMap = new HashMap<>();
    
    /** OpenSSL默认字段集合 */
    private static final Set<String> OPENSSL_FIELD = Set.of("C", "ST", "L", "O", "OU", "CN");

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化知识库客户端
        knowledgeBaseClient = new KnowledgeBaseClient();
        
        // 加载威胁检测所需的数据
        loadThreatDetectionData();
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("分析证书威胁特征，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 执行各种威胁检测
        detectBotnetThreats(certificate, labels);
        detectAPTThreats(certificate, labels);
        detectTorThreats(certificate, labels);
        detectC2Threats(certificate, labels);
        detectMaliciousThreats(certificate, labels);
        detectSpecialSignatures(certificate, labels);

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 加载威胁检测所需的数据
     */
    private void loadThreatDetectionData() throws IOException {
        try {
            // 从知识库服务加载威胁情报数据
            c2ThreatDomainList = knowledgeBaseClient.getC2ThreatDomains();
            c2ThreatIpList = knowledgeBaseClient.getC2ThreatIps();
            maliciousDomainList = knowledgeBaseClient.getMaliciousDomains();
            iocIpList = knowledgeBaseClient.getIocIps();
            mineDomainList = knowledgeBaseClient.getMineDomains();
            trancoTopDomainList = knowledgeBaseClient.getTrancoTopDomains();
            trancoTopDomainMap = knowledgeBaseClient.getTrancoTopDomainMap();

            log.info("成功从知识库加载威胁检测数据: C2域名{}, C2 IP{}, 恶意域名{}, IOC IP{}, 挖矿域名{}, Tranco域名{}",
                    c2ThreatDomainList.size(), c2ThreatIpList.size(), maliciousDomainList.size(),
                    iocIpList.size(), mineDomainList.size(), trancoTopDomainList.size());
                    
        } catch (Exception e) {
            log.error("从知识库加载威胁检测数据失败，使用默认值", e);
            // 使用默认的空集合
            c2ThreatDomainList = new HashSet<>();
            c2ThreatIpList = new HashSet<>();
            maliciousDomainList = new HashSet<>();
            iocIpList = new HashSet<>();
            mineDomainList = new HashSet<>();
            trancoTopDomainList = new HashSet<>();
            trancoTopDomainMap = new HashMap<>();
            throw new IOException("从知识库加载威胁检测数据失败", e);
        }
    }

    /**
     * 检测Botnet威胁
     */
    private void detectBotnetThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        // DanaBot检测 - 使用NLP检测多词名词
        detectDanaBot(certificate, labels);
        
        // Stealc检测 - IP地址作为CN且自签名
        detectStealc(certificate, labels);
        
        // Quakbot检测 - 使用NLP检测
        detectQuakbot(certificate, labels);
    }

    /**
     * 检测APT威胁
     */
    private void detectAPTThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        // APT28检测 - 基于标签组合
        if (labels.contains(CertificateLabel.FREE_CERTIFICATE) && 
            labels.contains(CertificateLabel.RECENTLY_REGISTERED) &&
            labels.contains(CertificateLabel.UNHOT_TLD)) {
            labels.add(CertificateLabel.APT28_CERT);
        }
        
        // APT29检测 - 基于标签组合
        if (labels.contains(CertificateLabel.LOST_CERT_LIST) && 
            labels.contains(CertificateLabel.SPECIAL_KEY_ID) &&
            labels.contains(CertificateLabel.IP_IN_SAN) && 
            labels.contains(CertificateLabel.WILDCARD_IN_ISSUER) &&
            labels.contains(CertificateLabel.LONG_VALIDITY_CERT)) {
            labels.add(CertificateLabel.APT29_CERT);
        }
        
        // APT PatchWork检测 - 自签名且CN为testexp
        Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());
        String subjectCN = subjectInfo.getOrDefault("CN", "").toString();
        if (labels.contains(CertificateLabel.SELF_SIGNED_CERT) && "testexp".equals(subjectCN)) {
            labels.add(CertificateLabel.APT_PATCHWORK);
        }
    }

    /**
     * 检测Tor威胁
     */
    private void detectTorThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());
        String subjectCN = subjectInfo.getOrDefault("CN", "").toString();
        String checkResult = TorAddressValidator.validateTorAddress(subjectCN);

        if (TorAddressValidator.TOR_V3.equals(checkResult)) {
            labels.add(CertificateLabel.TOR_V3_CERT);
            labels.add(CertificateLabel.NETWORK_PENETRATION_CERT);
        } else if (TorAddressValidator.TOR_V2.equals(checkResult)) {
            labels.add(CertificateLabel.TOR_V2_CERT);
            labels.add(CertificateLabel.NETWORK_PENETRATION_CERT);
        }
    }

    /**
     * 检测C&C威胁
     */
    private void detectC2Threats(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        List<String> ips = certificate.getCertificateIps();
        
        // 检查域名
        for (String domain : domains) {
            if (c2ThreatDomainList.contains(domain)) {
                labels.add(CertificateLabel.CC_CERT);
                return;
            }
        }
        
        // 检查IP
        for (String ip : ips) {
            if (c2ThreatIpList.contains(ip)) {
                labels.add(CertificateLabel.CC_CERT);
                return;
            }
        }
    }

    /**
     * 检测其他恶意威胁
     */
    private void detectMaliciousThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        List<String> ips = certificate.getCertificateIps();
        
        // 检测恶意域名
        for (String domain : domains) {
            if (maliciousDomainList.contains(domain)) {
                labels.add(CertificateLabel.MALICIOUS_DOMAIN_CERT);
                break;
            }
        }
        
        // 检测IOC IP
        for (String ip : ips) {
            if (iocIpList.contains(ip)) {
                labels.add(CertificateLabel.IOC_IP_CERT);
                break;
            }
        }
        
        // 检测挖矿证书
        for (String domain : domains) {
            if (mineDomainList.contains(domain)) {
                labels.add(CertificateLabel.MINING_CERT);
                break;
            }
        }
        
        // 检测分布式服务
        if (hasCommonSubdomain(domains)) {
            labels.add(CertificateLabel.DISTRIBUTED_SERVICES_CERT);
        }
        
        // 检测伪装合法域名
        detectFakeHotDomain(certificate, labels);
    }

    /**
     * 检测特殊签名
     */
    private void detectSpecialSignatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        // OpenSSL自签名检测
        if (labels.contains(CertificateLabel.SELF_SIGNED_CERT)) {
            Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());
            Set<String> subjectFields = subjectInfo.keySet();
            if (OPENSSL_FIELD.equals(subjectFields)) {
                labels.add(CertificateLabel.OPENSSL_SIGNED);
            }
        }
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }

    /**
     * 检测DanaBot威胁
     * 基于NLP技术检测多词名词组合
     */
    private void detectDanaBot(X509Certificate certificate, Set<CertificateLabel> labels) {
        // TODO: 实现基于NLP的DanaBot检测逻辑
        // 这里需要集成OpenNLP或其他NLP库来检测特定的词汇模式
        log.debug("DanaBot检测功能待实现");
    }

    /**
     * 检测Stealc威胁
     * 特征：IP地址作为CN且自签名，subject和issuer相同且只有一个字段
     */
    private void detectStealc(X509Certificate certificate, Set<CertificateLabel> labels) {
        Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());
        Map<String, Object> issuerInfo = CertificateNameParser.parse(certificate.getIssuer());

        String subjectCN = subjectInfo.getOrDefault("CN", "").toString();
        String issuerCN = issuerInfo.getOrDefault("CN", "").toString();

        if (NetworkUtils.isValidIp(subjectCN) && NetworkUtils.isValidIp(issuerCN) &&
            subjectCN.equals(issuerCN) && subjectInfo.size() == 1 && issuerInfo.size() == 1) {
            labels.add(CertificateLabel.BOTNET_STEALC);
        }
    }

    /**
     * 检测Quakbot威胁
     * 基于NLP技术检测特定模式
     */
    private void detectQuakbot(X509Certificate certificate, Set<CertificateLabel> labels) {
        // TODO: 实现基于NLP的Quakbot检测逻辑
        log.debug("Quakbot检测功能待实现");
    }

    /**
     * 检测是否有通用子域名
     * 用于识别分布式服务证书
     */
    private boolean hasCommonSubdomain(List<String> domains) {
        if (domains.size() < 2) {
            return false;
        }

        // 提取根域名
        Map<String, Integer> rootDomainCount = new HashMap<>();
        for (String domain : domains) {
            if (domain.startsWith("*.")) {
                domain = domain.substring(2);
            }

            // 简单的根域名提取逻辑
            String[] parts = domain.split("\\.");
            if (parts.length >= 2) {
                String rootDomain = parts[parts.length - 2] + "." + parts[parts.length - 1];
                rootDomainCount.put(rootDomain, rootDomainCount.getOrDefault(rootDomain, 0) + 1);
            }
        }

        // 如果有根域名出现多次，认为是分布式服务
        return rootDomainCount.values().stream().anyMatch(count -> count > 1);
    }

    /**
     * 检测伪装合法域名
     * 使用Levenshtein距离算法检测与Tranco Top域名的相似度
     */
    private void detectFakeHotDomain(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> associateDomains = certificate.getCertificateDomains();

        List<String> testDomainList = new ArrayList<>();

        // 过滤掉通配符域名和已知的热门域名
        for (String associateDomain : associateDomains) {
            if (associateDomain != null && associateDomain.startsWith("*.")) {
                associateDomain = associateDomain.substring(2);
            }
            if (!trancoTopDomainList.contains(associateDomain) && !testDomainList.contains(associateDomain)) {
                testDomainList.add(associateDomain);
            }
        }

        // 检测每个域名与热门域名的相似度
        for (String testDomain : testDomainList) {
            boolean foundMatch = false;
            try {
                for (String hotDomain : trancoTopDomainMap.keySet()) {
                    int levenshteinDistance = calculateLevenshteinDistance(testDomain, hotDomain);
                    // 如果编辑距离小于等于域名长度的20%，认为是伪装
                    if (levenshteinDistance <= (0.2 * hotDomain.length()) && levenshteinDistance > 0) {
                        labels.add(CertificateLabel.FAKE_HOT_DOMAIN);
                        foundMatch = true;
                        break;
                    }
                }
            } catch (Exception e) {
                log.debug("域名相似度计算失败: {}", testDomain);
            }
            if (foundMatch) {
                break;
            }
        }
    }

    /**
     * 计算Levenshtein距离
     */
    private int calculateLevenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];

        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }

        return dp[s1.length()][s2.length()];
    }
}
