package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.common.infrastructure.network.DomainUtils;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.Map;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 检测Stealc信息窃取软件使用的证书
 */
@Slf4j
public class StealcDetector extends BaseCertificateDetector {
    
    public StealcDetector() {
        super("Stealc Detector");
    }
    
    protected String getStringValue(Map<String, Object> map, String key) {
        if (map == null) return "";
        Object value = map.get(key);
        return value != null ? value.toString().toLowerCase() : "";
    }

    @Override
    public void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        Map<String, Object> subject = cert.getSubject();
        String subjectCN = getStringValue(subject, CertificateConstants.FIELD_CN);
        
        Map<String, Object> issuer = cert.getIssuer();
        String issuerCN = getStringValue(issuer, CertificateConstants.FIELD_CN);

        // 检查是否为IP地址且相同
        if (NetworkUtils.isValidIp(subjectCN) && 
            NetworkUtils.isValidIp(issuerCN) && 
            subjectCN.equals(issuerCN) && 
            subject.size() == 1 && 
            issuer.size() == 1) {
            
            cert.getLabels().add(CertificateLabel.BOTNET);
            log.debug("Detected potential Stealc certificate: {}", subjectCN);
        }
    }
}
