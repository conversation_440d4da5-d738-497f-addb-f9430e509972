package com.geeksec.certificateanalyzer.pipeline.preprocessing.deduplication;

import static com.geeksec.certificateanalyzer.function.DeduplicationOutputTag.Dedup_cert;
import static com.geeksec.certificateanalyzer.function.DeduplicationOutputTag.Not_Dedup_cert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.enums.CertificateSource;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.pipeline.common.selectors.CertKeySelector;
import com.geeksec.certificateanalyzer.util.db.doris.DorisUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/2/14
 */
@Slf4j
public class CertificateDeduplicationWindow {
    public static SingleOutputStreamOperator<X509Certificate> certificateDeduplicationWindow(DataStream<X509Certificate> certStream){
        SingleOutputStreamOperator<X509Certificate> certStreamWithTime = certStream
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps()).name("系统时间戳水印").setParallelism(4)
                .keyBy(new CertKeySelector(4))
                .window(TumblingProcessingTimeWindows.of(Time.milliseconds(500)))
                .process(new ProcessWindowFunction<X509Certificate, X509Certificate, Integer, TimeWindow>() {
                    @Override
                    public void process(Integer integer, ProcessWindowFunction<X509Certificate, X509Certificate, Integer, TimeWindow>.Context context, Iterable<X509Certificate> iterable, Collector<X509Certificate> collector) throws Exception {
                        List<String> userCertSha1List = new ArrayList<>();
                        for (X509Certificate cert : iterable) {
                            CertificateSource certSource = cert.getSource();
                            if (certSource != null && certSource.isUserCertificate()) {
                                userCertSha1List.add(cert.getDerSha1());
                            } else {
                                log.error("出现了预期外的证书来源类型");
                            }
                        }

                        try {
                            // 使用Doris批量查询用户证书
                            Map<String, Map<String, Object>> userResult = new HashMap<>();
                            if (!userCertSha1List.isEmpty()) {
                                userResult = DorisUtils.batchQueryCertsBySha1(userCertSha1List);
                            }

                            for (X509Certificate cert : iterable) {
                                CertificateSource certSource = cert.getSource();
                                if (certSource != null && certSource.isUserCertificate()) {
                                    String certSha1 = cert.getDerSha1();
                                    if (!userResult.containsKey(certSha1)) {
                                        cert.setCertOccurrenceCount(1);
                                        context.output(Not_Dedup_cert, cert);
                                    } else {
                                        // 这一步是从User重复证书中提取所需要的数据，后续可以再加
                                        getDedupCertInfo(cert, userResult);
                                        getNewUserIdList(cert, userResult);
                                        context.output(Dedup_cert, cert);
                                    }
                                } else {
                                    log.error("出现了预期外的证书来源类型");
                                }
                            }
                        } catch (Exception e) {
                            log.error("证书去重查询失败", e);
                            throw new RuntimeException(e);
                        }
                    }
                }).name("处理时间窗口函数去重所有来源证书").setParallelism(16);

        return certStreamWithTime;
    }

    /**
     * 从去重结果中获取证书信息
     *
     * @param cert 证书对象
     * @param result 查询结果
     */
    public static void getDedupCertInfo(X509Certificate cert, Map<String, Map<String, Object>> result) {
        String certSha1 = cert.getCertSha1();
        Map<String, Object> resultCert = result.get(certSha1);

        // 增加证书出现次数
        cert.setCertOccurrenceCount(cert.getCertOccurrenceCount() + 1);

        // 设置证书基本信息
        @SuppressWarnings("unchecked")
        LinkedHashMap<String, String> subject = (LinkedHashMap<String, String>) resultCert.getOrDefault("Subject", new LinkedHashMap<>());
        cert.setSubject(subject);

        @SuppressWarnings("unchecked")
        LinkedHashMap<String, String> issuer = (LinkedHashMap<String, String>) resultCert.getOrDefault("Issuer", new LinkedHashMap<>());
        cert.setIssuer(issuer);

        // 设置标签信息（注意：新版本使用Integer列表）
        @SuppressWarnings("unchecked")
        List<Integer> labels = (List<Integer>) resultCert.getOrDefault("Labels", new ArrayList<>());
        cert.setLabels(labels);
    }

    /**
     * 更新证书的用户ID列表
     *
     * @param userIdList 用户ID列表
     * @param certSha1 证书SHA1
     */
    public static void updateCertUserList(List<String> userIdList, String certSha1) {
        try {
            DorisUtils.updateCertUserIdList(certSha1, userIdList);
        } catch (Exception e) {
            log.error("更新证书用户ID列表失败，certSha1: {}", certSha1, e);
        }
    }

    /**
     * 获取并更新新的用户ID列表
     *
     * @param cert 证书对象
     * @param result 查询结果
     */
    public static void getNewUserIdList(X509Certificate cert, Map<String, Map<String, Object>> result) {
        String certSha1 = cert.getCertSha1();
        Map<String, Object> resultCert = result.get(certSha1);

        @SuppressWarnings("unchecked")
        List<String> existingUserIdList = (List<String>) resultCert.get("UserIDList");
        List<String> currentUserIdList = cert.getUserIdList();

        Set<String> newUserIdSet = new HashSet<>();
        if (existingUserIdList != null) {
            newUserIdSet.addAll(existingUserIdList);
        }
        if (currentUserIdList != null) {
            newUserIdSet.addAll(currentUserIdList);
        }

        List<String> newUserIdList = new ArrayList<>(newUserIdSet);
        if (!newUserIdList.equals(existingUserIdList)) {
            updateCertUserList(newUserIdList, certSha1);
            log.info("向SHA1为——{}——的证书更新用户ID", certSha1);
        } else {
            log.info("无需更新证书的用户ID");
        }

        cert.setUserIdList(newUserIdList);
    }
}

