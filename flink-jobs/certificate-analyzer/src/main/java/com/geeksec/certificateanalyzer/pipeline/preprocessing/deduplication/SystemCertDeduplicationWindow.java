package com.geeksec.certificateanalyzer.pipeline.preprocessing.deduplication;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessAllWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.db.doris.DorisUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/10/8
 */
@Slf4j
public class SystemCertDeduplicationWindow {
    public static SingleOutputStreamOperator<X509Certificate> systemCertificateDeduplicationWindow(
            DataStream<X509Certificate> systemStream) {
        SingleOutputStreamOperator<X509Certificate> systemStreamWithTime = systemStream
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps()).name("系统时间戳水印")
                .setParallelism(2)
                // .keyBy(new CertKeySelector(4))
                .windowAll(TumblingEventTimeWindows.of(Time.milliseconds(100)))
                .process(new ProcessAllWindowFunction<X509Certificate, X509Certificate, TimeWindow>() {
                    @Override
                    public void process(ProcessAllWindowFunction<X509Certificate, X509Certificate, TimeWindow>.Context context,
                            Iterable<X509Certificate> iterable, Collector<X509Certificate> collector) throws Exception {

                        List<String> certSha1List = new ArrayList<>();
                        for (X509Certificate cert : iterable) {
                            certSha1List.add(cert.getCertSha1());
                        }

                        try {
                            // 使用Doris批量查询系统证书（cert_source = 0表示系统证书）
                            Map<String, Map<String, Object>> existingCerts = DorisUtils
                                    .batchQueryCertsBySha1(certSha1List);

                            for (X509Certificate cert : iterable) {
                                String certSha1 = cert.getCertSha1();
                                Map<String, Object> existingCert = existingCerts.get(certSha1);

                                // 如果证书不存在，或者存在但不是系统证书，则输出
                                if (existingCert == null ||
                                        !"System".equals(existingCert.get("CertSource"))) {
                                    collector.collect(cert);
                                }
                            }
                        } catch (Exception e) {
                            log.error("系统证书去重查询失败", e);
                            // 发生异常时，输出所有证书以避免数据丢失
                            for (X509Certificate cert : iterable) {
                                collector.collect(cert);
                            }
                        }
                    }
                }).name("处理时间窗口函数去重证书").setParallelism(1);

        return systemStreamWithTime;
    }
}
