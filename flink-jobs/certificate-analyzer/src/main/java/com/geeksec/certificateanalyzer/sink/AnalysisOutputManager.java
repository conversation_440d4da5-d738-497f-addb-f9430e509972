package com.geeksec.certificateanalyzer.sink.analysis;

import org.apache.flink.streaming.api.datastream.DataStream;

import com.geeksec.certificateanalyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 分析结果输出管理器
 * 负责管理所有与分析结果相关的输出
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
@Slf4j
public class AnalysisOutputManager {

    /**
     * 添加分析结果相关的输出
     *
     * @param certificateStream 证书数据流
     */
    public static void addAnalysisOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加分析结果相关输出");

        // 分析结果消息队列输出（Kafka）
        if (CertificateAnalyzerConfig.isKafkaEnabled()) {
            certificateStream.addSink(new com.geeksec.certificateanalyzer.output.kafka.KafkaOutputSink())
                    .name("分析结果Kafka输出")
                    .setParallelism(CertificateAnalyzerConfig.getKafkaParallelism());
            log.info("已添加分析结果Kafka输出");
        }

        // 分析结果数据库输出（PostgreSQL）
        if (CertificateAnalyzerConfig.isPostgreSQLEnabled()) {
            // TODO: 实现PostgreSQL输出
            // certificateStream.addSink(new com.geeksec.certificateanalyzer.output.postgresql.PostgreSQLErrorSink())
            //         .name("分析结果PostgreSQL输出")
            //         .setParallelism(1);
            log.info("PostgreSQL输出已启用但尚未实现");
        }

        log.info("分析结果相关输出添加完成");
    }

    /**
     * 添加威胁分析输出
     *
     * @param certificateStream 证书数据流
     */
    public static void addThreatAnalysisOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加威胁分析输出");

        // 过滤出包含威胁标签的证书
        DataStream<X509Certificate> threatCertStream = certificateStream
                .filter(cert -> cert.getLabels().stream()
                        .anyMatch(tag -> tag.contains("Threat") || tag.contains("Malicious") || 
                                       tag.contains("Suspicious") || tag.contains("APT")))
                .name("威胁证书过滤");

        // 威胁证书特殊处理输出
        if (CertificateAnalyzerConfig.isKafkaEnabled()) {
            threatCertStream.addSink(new ThreatAnalysisKafkaSink())
                    .name("威胁分析Kafka输出")
                    .setParallelism(2);
            log.info("已添加威胁分析Kafka输出");
        }

        log.info("威胁分析输出添加完成");
    }

    /**
     * 威胁分析专用Kafka输出
     */
    private static class ThreatAnalysisKafkaSink extends com.geeksec.certificateanalyzer.output.kafka.KafkaOutputSink {
        // 可以在这里自定义威胁分析的特殊输出逻辑
        // 比如发送到不同的Kafka主题，或者添加额外的元数据
    }
}
