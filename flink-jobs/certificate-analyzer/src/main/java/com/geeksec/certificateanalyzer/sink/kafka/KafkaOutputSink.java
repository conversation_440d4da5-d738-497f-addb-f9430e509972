package com.geeksec.certificateanalyzer.output.kafka;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.kafka.clients.producer.ProducerRecord;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.config.ConfigurationManager;

import lombok.extern.slf4j.Slf4j;

/**
 * Kafka输出接收器，用于将数据写入Kafka
 *
 * <AUTHOR>
 * @Date 2023/6/5
 */
@Slf4j
public class KafkaOutputSink {

    private static final Properties KAFKA_SINK_PROPERTIES = new Properties();

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /**
     * Kafka输出主题
     */
    public static final String OUTPUT_TOPIC = CONFIG.get("kafka.output.topic", "certificate-output");

    /**
     * Kafka代理服务器列表
     */
    private static String outputBootstrapServers = "";

    /**
     * 将告警数据写入Kafka
     *
     * @param alarmJsonStream 告警JSON数据流
     * @param kafkaInfo       Kafka连接信息
     * @throws Exception 如果写入过程中发生错误
     */
    public static void alarmKafkaSink(DataStream<JSONObject> alarmJsonStream, Map<String, Object> kafkaInfo)
            throws Exception {
        outputBootstrapServers = kafkaInfo.get("ip") + ":" + kafkaInfo.get("port");
        log.info("Kafka broker list: {}", outputBootstrapServers);

        KafkaRecordSerializationSchema<String> kafkaRecordSerializationSchema = new KafkaRecordSerializationSchema<String>() {
            @Override
            public ProducerRecord<byte[], byte[]> serialize(String s, KafkaSinkContext context, Long timestamp) {
                String time = String.valueOf(System.currentTimeMillis());
                return new ProducerRecord<>(
                        OUTPUT_TOPIC,
                        time.getBytes(),
                        s.getBytes());
            }
        };

        // KAFKA_SINK_PROPERTIES is now passed to setKafkaProducerConfig, no need to set bootstrap.servers here directly
        // KAFKA_SINK_PROPERTIES.put("bootstrap.servers", outputBrokerList);
        KAFKA_SINK_PROPERTIES.put("transaction.timeout.ms", "5000");

        KafkaSink<String> kafkaSink = KafkaSink.<String>builder()
                .setBootstrapServers(outputBootstrapServers)
                .setRecordSerializer(kafkaRecordSerializationSchema)
                .setKafkaProducerConfig(KAFKA_SINK_PROPERTIES) // Pass all properties here
                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE) // AT_LEAST_ONCE is default, but explicit for clarity
                .build();
        DataStream<String> alarmStringStream = alarmJsonStream.map(new MapFunction<JSONObject, String>() {
            @Override
            public String map(JSONObject jsonObject) throws Exception {
                return jsonObject.toString();
            }
        });
        alarmStringStream.sinkTo(kafkaSink).name("kafka 外发告警日志").setParallelism(2);
    }

    /**
     * 将标签值转换为威胁标签
     *
     * @param labelsValue 标签值列表
     * @return 威胁标签列表
     */
    private static List<Map<String, Object>> tranTagsToThreatTag(List<String> labelsValue) {
        List<Map<String, Object>> threatCertTagList = new ArrayList<>(labelsValue.size());
        for (String tag : labelsValue) {
            Map<String, Object> threatCertTag = new HashMap<>(4);
            Integer tagId = Integer.parseInt(tag);
            Map<String, String> tagInfo = LABEL_INFO_LIST.getOrDefault(tag, new HashMap<>(0));
            Integer blackList = Integer.parseInt(tagInfo.get("Black_List"));
            String tagLevel = "";

            if (0 < blackList && blackList < 40) {
                tagLevel = "low";
            } else if (40 <= blackList && blackList < 70) {
                tagLevel = "medium";
            } else if (70 <= blackList && blackList <= 100) {
                tagLevel = "high";
            }

            if (!"".equals(tagLevel)) {
                threatCertTag.put("tag_id", tagId);
                threatCertTag.put("tag_name", tagInfo.getOrDefault("Tag_Text", "unk"));
                threatCertTag.put("tag_level", tagLevel);
                threatCertTagList.add(threatCertTag);
            }
        }
        return threatCertTagList;
    }
}
